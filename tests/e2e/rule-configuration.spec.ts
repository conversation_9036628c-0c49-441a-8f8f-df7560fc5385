import { test, expect } from '@playwright/test'

test.describe('重复判断规则配置', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到规则配置页面
    await page.goto('/index/reportIntegrationSpotCheck/ruleConfiguration')
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle')
    
    // 清空本地存储，确保测试环境干净
    await page.evaluate(() => {
      localStorage.clear()
    })
    
    // 刷新页面以应用清空的存储
    await page.reload()
    await page.waitForLoadState('networkidle')
  })

  test('页面基本元素显示正确', async ({ page }) => {
    // 验证左侧导航菜单
    await expect(page.locator('.sidebar-title')).toContainText('规则配置')
    await expect(page.locator('.sidebar-menu')).toBeVisible()
    await expect(page.locator('text=重复判断')).toBeVisible()
    
    // 验证主内容区域
    await expect(page.locator('button:has-text("添加规则")')).toBeVisible()
    
    // 验证表格存在
    await expect(page.locator('.el-table')).toBeVisible()
    
    // 验证分页组件
    await expect(page.locator('.el-pagination')).toBeVisible()
  })

  test('表格列头显示正确', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.el-table', { timeout: 10000 })
    
    // 验证表格列头
    const headers = [
      '创建时间', 
      '报表A类型',
      '报表A字段',
      '报表B类型', 
      '报表B字段',
      '匹配方式',
      '操作'
    ]
    
    for (const header of headers) {
      await expect(page.locator('.el-table th').filter({ hasText: header })).toBeVisible()
    }
  })

  test('添加规则功能', async ({ page }) => {
    // 点击添加规则按钮
    await page.click('button:has-text("添加规则")')
    
    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toBeVisible()
    await expect(page.locator('.el-dialog__title')).toContainText('添加规则')
    
    // 验证表单字段
    await expect(page.locator('label:has-text("报表A类型")')).toBeVisible()
    await expect(page.locator('label:has-text("报表A字段")')).toBeVisible()
    await expect(page.locator('label:has-text("报表B类型")')).toBeVisible()
    await expect(page.locator('label:has-text("报表B字段")')).toBeVisible()
    await expect(page.locator('label:has-text("匹配方式")')).toBeVisible()
    
    // 填写表单
    // 选择报表A类型
    await page.click('.el-form-item:has(label:text("报表A类型")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("业务表")')
    
    // 选择报表A字段
    await page.click('.el-form-item:has(label:text("报表A字段")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("电话")')
    
    // 选择报表B类型
    await page.click('.el-form-item:has(label:text("报表B类型")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("临时表")')
    
    // 选择报表B字段
    await page.click('.el-form-item:has(label:text("报表B字段")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("手机")')
    
    // 选择匹配方式
    await page.click('.el-form-item:has(label:text("匹配方式")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("完全匹配")')
    
    // 提交表单
    await page.click('button:has-text("确定")')
    
    // 验证成功消息
    await expect(page.locator('.el-message--success')).toBeVisible({ timeout: 5000 })
    
    // 验证弹窗关闭
    await expect(page.locator('.el-dialog')).not.toBeVisible()
    
    // 验证新规则出现在表格中
    await expect(page.locator('.el-table tbody tr').first()).toBeVisible()
  })

  test('编辑规则功能', async ({ page }) => {
    // 先添加一条规则
    await page.click('button:has-text("添加规则")')
    await page.waitForSelector('.el-dialog')
    
    // 快速填写表单
    await page.click('.el-form-item:has(label:text("报表A类型")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("业务表")')
    
    await page.click('.el-form-item:has(label:text("报表A字段")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("姓名")')
    
    await page.click('.el-form-item:has(label:text("报表B类型")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("临时表")')
    
    await page.click('.el-form-item:has(label:text("报表B字段")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("姓名")')
    
    await page.click('.el-form-item:has(label:text("匹配方式")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("完全匹配")')
    
    await page.click('button:has-text("确定")')
    await page.waitForSelector('.el-message--success')
    
    // 等待表格更新
    await page.waitForTimeout(1000)
    
    // 点击编辑按钮
    await page.click('.el-table tbody tr:first-child button:has-text("编辑")')
    
    // 验证弹窗打开且标题为编辑
    await expect(page.locator('.el-dialog__title')).toContainText('编辑规则')
    
    // 修改报表A字段
    await page.click('.el-form-item:has(label:text("报表A字段")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("身份证")')
    
    // 提交修改
    await page.click('button:has-text("确定")')
    
    // 验证成功消息
    await expect(page.locator('.el-message--success')).toBeVisible()
  })

  test('删除规则功能', async ({ page }) => {
    // 先添加一条规则
    await page.click('button:has-text("添加规则")')
    await page.waitForSelector('.el-dialog')
    
    // 填写并提交表单
    await page.click('.el-form-item:has(label:text("报表A类型")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("业务表")')
    
    await page.click('.el-form-item:has(label:text("报表A字段")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("电话")')
    
    await page.click('.el-form-item:has(label:text("报表B类型")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("临时表")')
    
    await page.click('.el-form-item:has(label:text("报表B字段")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("手机")')
    
    await page.click('.el-form-item:has(label:text("匹配方式")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("模糊匹配")')
    
    await page.click('button:has-text("确定")')
    await page.waitForSelector('.el-message--success')
    
    // 等待表格更新
    await page.waitForTimeout(1000)
    
    // 点击删除按钮
    await page.click('.el-table tbody tr:first-child button:has-text("删除")')
    
    // 确认删除
    await page.click('.el-message-box button:has-text("确定")')
    
    // 验证成功消息
    await expect(page.locator('.el-message--success')).toBeVisible()
  })

  test('数据持久化功能', async ({ page }) => {
    // 添加一条规则
    await page.click('button:has-text("添加规则")')
    await page.waitForSelector('.el-dialog')
    
    await page.click('.el-form-item:has(label:text("报表A类型")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("业务表")')
    
    await page.click('.el-form-item:has(label:text("报表A字段")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("地址")')
    
    await page.click('.el-form-item:has(label:text("报表B类型")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("业务表")')
    
    await page.click('.el-form-item:has(label:text("报表B字段")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("地址")')
    
    await page.click('.el-form-item:has(label:text("匹配方式")) .el-select')
    await page.click('.el-select-dropdown__item:has-text("正则匹配")')
    
    await page.click('button:has-text("确定")')
    await page.waitForSelector('.el-message--success')
    
    // 刷新页面
    await page.reload()
    await page.waitForLoadState('networkidle')
    
    // 验证数据仍然存在
    await expect(page.locator('text=地址')).toBeVisible()
    await expect(page.locator('text=正则匹配')).toBeVisible()
  })

  test('表单验证功能', async ({ page }) => {
    // 点击添加规则
    await page.click('button:has-text("添加规则")')
    await page.waitForSelector('.el-dialog')
    
    // 直接点击确定，不填写任何字段
    await page.click('button:has-text("确定")')
    
    // 验证验证错误消息存在
    await expect(page.locator('.el-form-item__error')).toHaveCount(5)
    
    // 验证弹窗仍然打开
    await expect(page.locator('.el-dialog')).toBeVisible()
  })

  test('分页功能', async ({ page }) => {
    // 验证分页组件存在
    await expect(page.locator('.el-pagination')).toBeVisible()
    
    // 验证页码显示
    await expect(page.locator('.el-pagination .el-pager')).toBeVisible()
    
    // 验证每页显示数量选择器
    await expect(page.locator('.el-pagination .el-pagination__sizes')).toBeVisible()
  })

  test('左侧导航菜单交互', async ({ page }) => {
    // 点击重复判断菜单项
    await page.click('text=重复判断')
    
    // 验证菜单项被激活
    await expect(page.locator('.el-menu-item.is-active')).toContainText('重复判断')
  })
})
