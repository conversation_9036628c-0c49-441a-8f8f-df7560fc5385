<script setup lang="ts" name="configTemplate">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Delete, Check, Close, ArrowLeft } from '@element-plus/icons-vue'

// 路由实例
const router = useRouter()

// 搜索表单
const searchFormProp = ref([
  {
    label: '模板名称',
    prop: 'templateName',
    type: 'text',
    style: { width: '200px' }
  },
  {
    label: '类别',
    prop: 'category',
    type: 'select',
    style: { width: '150px' },
    options: [
      { label: '全部', value: '' },
      { label: '土地类', value: '土地类' },
      { label: '财政类', value: '财政类' },
      { label: '医保类', value: '医保类' },
      { label: '教育类', value: '教育类' }
    ]
  }
])
const searchForm = ref({ templateName: '', category: '' })

// 加载状态
const loading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0)
const currentRow = ref(null)

// 操作按钮配置
const buttons = [
  { label: '导出', type: 'primary', code: 'export' },
  { label: '复制', type: 'info', code: 'copy' },
  { label: '查看', type: 'success', code: 'view' },
  { label: '历史', type: 'warning', code: 'history' },
  { label: '编辑', type: 'primary', code: 'edit' },
  { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' }
]

// 表头配置
const columns = [
  { prop: 'templateName', label: '抽查模板名称' },
  { prop: 'category', label: '类别' },
  { prop: 'createTime', label: '创建时间' },
  { prop: 'description', label: '模板描述' },
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 列表请求参数
const reqParams = reactive({
  templateName: '',
  category: '',
  skipCount: 0,
  maxResultCount: 10
})

// 表格数据
const tableData = ref<any[]>([])
const selectedRows = ref<any[]>([])

// 配置抽查模板数据类型定义
interface ConfigTemplate {
  id: string
  index: number
  templateName: string
  category: string
  createTime: string
  description: string
  creator: string
  updateTime: string
}

// 生成模拟数据
const generateMockData = (): ConfigTemplate[] => {
  const categories = ['土地类', '财政类', '医保类', '教育类']
  const templateNames = [
    '财政局预算表用模板', '医保局报销表用模板', '土地局审批表用模板', '教育局统计表用模板',
    '财政收支明细模板', '医保费用核算模板', '土地使用权模板', '教育经费统计模板',
    '财政专项资金模板', '医保政策执行模板', '土地征收补偿模板', '教育质量评估模板',
    '财政预算执行模板', '医保基金监管模板', '土地规划审查模板', '教育资源配置模板',
    '财政绩效评价模板', '医保服务监督模板', '土地开发利用模板', '教育发展规划模板',
    '财政资金监管模板', '医保待遇审核模板', '土地权属登记模板', '教育督导评估模板',
    '财政投资项目模板', '医保异地结算模板', '土地市场监测模板', '教育信息统计模板',
    '财政国库管理模板', '医保智能审核模板', '土地生态保护模板', '教育均衡发展模板'
  ]

  const descriptions = [
    '用于财政局预算表的标准抽查模板，包含预算编制、执行、监督等环节',
    '医保局报销数据的专用抽查模板，涵盖医疗费用审核和报销流程',
    '土地局审批流程的规范抽查模板，确保土地使用审批合规性',
    '教育局统计数据的通用抽查模板，统计教育资源配置情况',
    '财政收支明细的详细抽查模板，监控财政资金使用效率',
    '医保费用核算的精确抽查模板，保障医保基金安全运行',
    '土地使用权的专业抽查模板，规范土地权属管理',
    '教育经费统计的完整抽查模板，优化教育资源配置',
    '财政专项资金的监管抽查模板，提高资金使用效益',
    '医保政策执行的评估抽查模板，确保政策落实到位',
    '土地征收补偿的标准抽查模板，保护农民合法权益',
    '教育质量评估的科学抽查模板，提升教育教学质量',
    '财政预算执行的动态抽查模板，强化预算约束力',
    '医保基金监管的全面抽查模板，防范基金运行风险',
    '土地规划审查的严格抽查模板，促进土地集约利用',
    '教育资源配置的均衡抽查模板，推进教育公平发展'
  ]

  const data: ConfigTemplate[] = []
  for (let i = 1; i <= 28; i++) {
    const category = categories[Math.floor(Math.random() * categories.length)]
    const templateName = templateNames[Math.floor(Math.random() * templateNames.length)]
    const description = descriptions[Math.floor(Math.random() * descriptions.length)]

    const createTime = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000)
    const updateTime = new Date(createTime.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000)

    data.push({
      id: `template_${i}`,
      index: i,
      templateName: `${templateName}${String(i).padStart(2, '0')}`,
      category,
      createTime: createTime.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }),
      description,
      creator: `用户${Math.floor(Math.random() * 10) + 1}`,
      updateTime: updateTime.toISOString()
    })
  }
  return data.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
}

// 初始化数据
const initData = () => {
  const savedData = localStorage.getItem('configTemplateData')
  if (savedData) {
    tableData.value = JSON.parse(savedData)
  } else {
    tableData.value = generateMockData()
    localStorage.setItem('configTemplateData', JSON.stringify(tableData.value))
  }
  pagination.total = tableData.value.length
}

// 获取过滤后的数据
const getFilteredData = () => {
  let filtered = [...tableData.value]
  
  if (reqParams.templateName) {
    filtered = filtered.filter(item => 
      item.templateName.includes(reqParams.templateName)
    )
  }
  
  if (reqParams.category) {
    filtered = filtered.filter(item => item.category === reqParams.category)
  }
  
  return filtered
}

// 获取当前页数据
const getCurrentPageData = () => {
  const filtered = getFilteredData()
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  pagination.total = filtered.length
  return filtered.slice(start, end)
}

// 查询
const onSearch = async () => {
  try {
    loading.value = true
    
    // 模拟搜索延迟
    await new Promise(resolve => setTimeout(resolve, 800))
    
    pagination.page = 1
    reqParams.skipCount = 0
    reqParams.maxResultCount = pagination.size
    reqParams.templateName = searchForm.value.templateName
    reqParams.category = searchForm.value.category
    
    tableRef.value?.reload()
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    loading.value = false
  }
}

// 表格操作点击事件
const onTableClickButton = ({ row, btn }: any) => {
  switch (btn.code) {
    case 'export':
      handleExport(row)
      break
    case 'copy':
      handleCopy(row)
      break
    case 'view':
      handleView(row)
      break
    case 'history':
      handleHistory(row)
      break
    case 'edit':
      handleEdit(row)
      break
    case 'delete':
      handleDelete(row)
      break
  }
}

// 查看模板对话框相关
const viewDialogVisible = ref(false)
const currentViewTemplate = ref<any>(null)

// 编辑模板相关
const editMode = ref(false)
const editingTemplate = ref<any>(null)

// 操作处理函数
const handleExport = (row: any) => {
  try {
    // 创建导出数据
    const exportData = {
      模板名称: row.templateName,
      类别: row.category,
      创建时间: row.createTime,
      描述: row.description,
      创建者: row.creator,
      更新时间: row.updateTime
    }

    // 转换为JSON字符串
    const jsonString = JSON.stringify(exportData, null, 2)

    // 创建Blob对象
    const blob = new Blob([jsonString], { type: 'application/json' })

    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${row.templateName}_${new Date().getTime()}.json`

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 释放URL对象
    URL.revokeObjectURL(url)

    ElMessage.success(`模板 "${row.templateName}" 导出成功！`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const handleCopy = (row: any) => {
  try {
    // 生成新的模板数据
    const newTemplate = {
      id: `template_${Date.now()}`,
      index: tableData.value.length + 1,
      templateName: `${row.templateName}_副本`,
      category: row.category,
      createTime: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }),
      description: `${row.description}（副本）`,
      creator: '当前用户',
      updateTime: new Date().toISOString()
    }

    // 添加到表格数据开头
    tableData.value.unshift(newTemplate)

    // 保存到本地存储
    localStorage.setItem('configTemplateData', JSON.stringify(tableData.value))

    // 更新分页总数
    pagination.total = tableData.value.length

    ElMessage.success(`模板 "${row.templateName}" 复制成功！`)
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请重试')
  }
}

const handleView = (row: any) => {
  currentViewTemplate.value = row
  viewDialogVisible.value = true
}

const handleHistory = (row: any) => {
  // 设置当前模板并生成历史记录
  currentViewTemplate.value = row
  generateHistoryData()
  historyDialogVisible.value = true
}

const handleEdit = (row: any) => {
  editMode.value = true
  editingTemplate.value = { ...row }

  // 填充编辑表单
  templateForm.templateName = row.templateName
  templateForm.category = row.category
  templateForm.validationStatus = 'pending'
  templateForm.dataMatchType = ''
  templateForm.dataMatchStatus = 'pending'

  dialogVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确认删除模板 "${row.templateName}" 吗？删除后不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 从表格数据中移除该项目
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)

      // 更新本地存储
      localStorage.setItem('configTemplateData', JSON.stringify(tableData.value))

      // 更新分页总数
      pagination.total = tableData.value.length

      // 如果当前页没有数据，回到第一页
      if (getCurrentPageData().length === 0 && pagination.page > 1) {
        pagination.page = 1
      }

      ElMessage.success(`模板 "${row.templateName}" 删除成功！`)
    }
  } catch (error) {
    // 用户取消删除
    console.log('用户取消删除操作')
  }
}

// 新建模板对话框相关
const dialogVisible = ref(false)
const formRef = ref()

// 历史记录对话框相关
const historyDialogVisible = ref(false)
const currentTemplateHistory = ref<any[]>([])
const selectedHistoryTemplate = ref(null)

// 新建模板表单数据
const templateForm = reactive({
  templateName: '财税模板一',
  category: '',
  validationStatus: 'pending', // pending, success
  dataMatchType: '',
  dataMatchStatus: 'pending', // pending, success
  relationRules: [
    { id: 1, rule: '主键-外键关系检查' }
  ],
  cleaningRules: [
    { id: 1, rule: '缺失值处理' }
  ],
  dataSources: [
    {
      id: 1,
      algorithm: '',
      dataSource: '台川区企业所得税表',
      serverAddress: '',
      port: '',
      startTime: '2024-04-26 13:00:21',
      endTime: '2024-04-26 13:00:21',
      dataTypes: ['日报', '季报'],
      dataAmount: '100000'
    }
  ]
})

// 表单验证规则
const formRules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择模板分类', trigger: 'change' }
  ]
}

// 选项数据
const categoryOptions = [
  { label: '财税类', value: '财税类' },
  { label: '医保类', value: '医保类' },
  { label: '土地类', value: '土地类' },
  { label: '教育类', value: '教育类' }
]

const dataMatchOptions = [
  { label: '时间', value: '时间' },
  { label: '字符串', value: '字符串' }
]

const algorithmOptions = [
  { label: '算法1', value: '算法1' },
  { label: '算法2', value: '算法2' }
]

const dataSourceOptions = [
  { label: '台川区企业所得税表', value: '台川区企业所得税表' },
  { label: '江津区企业所得税表', value: '江津区企业所得税表' }
]

const relationRuleOptions = [
  '主键-外键关系检查',
  '业务逻辑一致性检查',
  '交叉验证检查',
  '时间序列一致性检查',
  '唯一性约束检查'
]

const cleaningRuleOptions = [
  '缺失值处理',
  '重复记录处理',
  '格式标准化',
  '异常值处理',
  '数据类型转换',
  '拼写纠正'
]

const dataTypeOptions = [
  { label: '日报', value: '日报' },
  { label: '季报', value: '季报' },
  { label: '年报', value: '年报' },
  { label: '月报', value: '月报' }
]

// 右上角按钮处理函数
const onClickAddTemplate = () => {
  // 重置表单
  resetTemplateForm()
  dialogVisible.value = true
}

const onClickTemplateUsageRecord = () => {
  // 生成历史记录数据
  generateHistoryData()
  historyDialogVisible.value = true
}

const onClickBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的模板')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedRows.value.length} 个模板吗？删除后不可恢复。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 获取选中行的ID
    const selectedIds = selectedRows.value.map(row => row.id)

    // 从表格数据中移除选中的项目
    tableData.value = tableData.value.filter(item => !selectedIds.includes(item.id))

    // 更新本地存储
    localStorage.setItem('configTemplateData', JSON.stringify(tableData.value))

    // 更新分页总数
    pagination.total = tableData.value.length

    // 清空选中状态
    selectedRows.value = []

    // 如果当前页没有数据，回到第一页
    if (getCurrentPageData().length === 0 && pagination.page > 1) {
      pagination.page = 1
    }

    ElMessage.success(`成功删除 ${selectedIds.length} 个模板`)
  } catch (error) {
    // 用户取消删除
    console.log('用户取消删除操作')
  }
}

// 重置表单
const resetTemplateForm = () => {
  templateForm.templateName = '财税模板一'
  templateForm.category = ''
  templateForm.validationStatus = 'pending'
  templateForm.dataMatchType = ''
  templateForm.dataMatchStatus = 'pending'
  templateForm.relationRules = [{ id: 1, rule: '主键-外键关系检查' }]
  templateForm.cleaningRules = [{ id: 1, rule: '缺失值处理' }]
  templateForm.dataSources = [{
    id: 1,
    algorithm: '',
    dataSource: '台川区企业所得税表',
    serverAddress: '',
    port: '',
    startTime: '2024-04-26 13:00:21',
    endTime: '2024-04-26 13:00:21',
    dataTypes: ['日报', '季报'],
    dataAmount: '100000'
  }]
}

// 重置搜索表单
const onReset = () => {
  searchForm.value = { templateName: '', category: '' }
  onSearch()
}

// 有效性检验
const startValidation = () => {
  templateForm.validationStatus = 'success'
  ElMessage.success('检验成功！')
}

// 数据匹配执行
const startDataMatch = () => {
  templateForm.dataMatchStatus = 'success'
  ElMessage.success('检验成功！')
}

// 添加关联规则
const addRelationRule = () => {
  const newId = Math.max(...templateForm.relationRules.map(r => r.id)) + 1
  templateForm.relationRules.push({
    id: newId,
    rule: '主键-外键关系检查'
  })
}

// 删除关联规则
const removeRelationRule = (id: number) => {
  const index = templateForm.relationRules.findIndex(r => r.id === id)
  if (index > -1) {
    templateForm.relationRules.splice(index, 1)
  }
}

// 添加数据清洗规则
const addCleaningRule = () => {
  const newId = Math.max(...templateForm.cleaningRules.map(r => r.id)) + 1
  templateForm.cleaningRules.push({
    id: newId,
    rule: '缺失值处理'
  })
}

// 删除数据清洗规则
const removeCleaningRule = (id: number) => {
  const index = templateForm.cleaningRules.findIndex(r => r.id === id)
  if (index > -1) {
    templateForm.cleaningRules.splice(index, 1)
  }
}

// 添加数据源
const addDataSource = () => {
  const newId = Math.max(...templateForm.dataSources.map(d => d.id)) + 1
  templateForm.dataSources.push({
    id: newId,
    algorithm: '',
    dataSource: '台川区企业所得税表',
    serverAddress: '',
    port: '',
    startTime: '2024-04-26 13:00:21',
    endTime: '2024-04-26 13:00:21',
    dataTypes: ['日报', '季报'],
    dataAmount: '100000'
  })
}

// 删除数据源
const removeDataSource = (id: number) => {
  if (templateForm.dataSources.length <= 1) {
    ElMessage.warning('至少需要保留一个数据源')
    return
  }
  const index = templateForm.dataSources.findIndex(d => d.id === id)
  if (index > -1) {
    templateForm.dataSources.splice(index, 1)
  }
}

// 保存模板
const saveTemplate = async () => {
  try {
    await formRef.value?.validate()

    if (editMode.value && editingTemplate.value) {
      // 编辑模式：更新现有模板
      const index = tableData.value.findIndex(item => item.id === editingTemplate.value.id)
      if (index > -1) {
        tableData.value[index] = {
          ...tableData.value[index],
          templateName: templateForm.templateName,
          category: templateForm.category,
          description: `${templateForm.category}的抽查模板，包含${templateForm.relationRules.length}个关联规则和${templateForm.cleaningRules.length}个数据清洗规则`,
          updateTime: new Date().toISOString()
        }
        ElMessage.success('模板更新成功！')
      }
    } else {
      // 新建模式：创建新模板
      const newTemplate = {
        id: `template_${Date.now()}`,
        index: tableData.value.length + 1,
        templateName: templateForm.templateName,
        category: templateForm.category,
        createTime: new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }),
        description: `${templateForm.category}的抽查模板，包含${templateForm.relationRules.length}个关联规则和${templateForm.cleaningRules.length}个数据清洗规则`,
        creator: '当前用户',
        updateTime: new Date().toISOString()
      }

      // 添加到表格数据
      tableData.value.unshift(newTemplate)
      ElMessage.success('模板创建成功！')
    }

    // 保存到本地存储
    localStorage.setItem('configTemplateData', JSON.stringify(tableData.value))

    // 更新分页总数
    pagination.total = tableData.value.length

    // 关闭对话框并重置状态
    dialogVisible.value = false
    editMode.value = false
    editingTemplate.value = null
  } catch (error) {
    console.error('保存失败:', error)
  }
}

// 取消创建/编辑
const cancelCreate = () => {
  dialogVisible.value = false
  editMode.value = false
  editingTemplate.value = null
}

// 生成历史记录数据
const generateHistoryData = () => {
  const historyData = []
  for (let i = 1; i <= 5; i++) {
    const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
    historyData.push({
      id: i,
      序号: String(i).padStart(2, '0'),
      创建时间: date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }),
      version: `v1.${i}`,
      description: `第${i}次修改记录`
    })
  }
  currentTemplateHistory.value = historyData
}

// 查看历史记录
const viewHistoryTemplate = (row: any) => {
  ElMessage.info(`查看历史记录: ${row.序号} - ${row.创建时间}`)
}

// 使用历史模板
const useHistoryTemplate = (row: any) => {
  selectedHistoryTemplate.value = row
  ElMessage.success(`已选择使用历史模板: ${row.序号}`)
}

// 确认使用历史模板
const confirmUseHistory = () => {
  if (selectedHistoryTemplate.value) {
    ElMessage.success(`已应用历史模板: ${selectedHistoryTemplate.value.序号}`)
  }
  historyDialogVisible.value = false
  selectedHistoryTemplate.value = null
}

// 取消历史记录操作
const cancelHistory = () => {
  historyDialogVisible.value = false
  selectedHistoryTemplate.value = null
}

// 获取标签类型
const getTagType = (category: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '财税类': 'primary',
    '医保类': 'success',
    '土地类': 'warning',
    '教育类': 'info'
  }
  return typeMap[category] || 'primary'
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
    reqParams.skipCount = (val - 1) * pagination.size
  } else {
    pagination.size = val
    reqParams.maxResultCount = pagination.size
    pagination.page = 1
  }
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 返回上一页
const handleGoBack = () => {
  router.push('/reportIntegrationSpotCheck')
}

// 初始化
onMounted(() => {
  initData()
})
</script>

<template>
  <div class="config-template">
    <Block title="配置抽查模板" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <el-button
          size="small"
          type="default"
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>

        <el-button size="small" type="primary" @click="onClickAddTemplate">新建抽查模板</el-button>
        <el-button size="small" type="info" @click="onClickTemplateUsageRecord">抽查模板使用记录</el-button>
        <el-button size="small" type="danger" @click="onClickBatchDelete">批量删除</el-button>
      </template>
      
      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <el-form :model="searchForm" inline class="search-form">
            <el-form-item label="模板名称" label-width="80px">
              <el-input
                v-model="searchForm.templateName"
                placeholder="请输入模板名称"
                style="width: 200px"
                clearable
              />
            </el-form-item>
            <el-form-item label="类别" label-width="50px">
              <el-select
                v-model="searchForm.category"
                placeholder="请选择类别"
                style="width: 150px"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option label="土地类" value="土地类" />
                <el-option label="财政类" value="财政类" />
                <el-option label="医保类" value="医保类" />
                <el-option label="教育类" value="教育类" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button @click="onReset">
                <el-icon><Refresh /></el-icon>
                清空
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      
      <!-- 列表 -->
      <TableV2
        ref="tableRef"
        :defaultTableData="getCurrentPageData()"
        :columns="columns"
        :req-params="reqParams"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :height="tableHeight"
        :buttons="buttons"
        :loading="loading"
        @loading="loading = $event"
        @click-button="onTableClickButton"
        @selection-change="handleSelectionChange"
      />
      
      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      />
    </Block>

    <!-- 新建/编辑抽查模板对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editMode ? '编辑抽查模板' : '新建抽查模板'"
      width="900px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="templateForm"
        :rules="formRules"
        label-width="120px"
        class="template-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模板名称" prop="templateName" required>
                <el-input v-model="templateForm.templateName" placeholder="请输入模板名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模板分类" prop="category" required>
                <el-select v-model="templateForm.category" placeholder="请选择分类" style="width: 100%">
                  <el-option
                    v-for="option in categoryOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 有效性检验 -->
        <div class="form-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="有效性检验">
                <div class="validation-container">
                  <el-button
                    v-if="templateForm.validationStatus === 'pending'"
                    type="primary"
                    size="small"
                    @click="startValidation"
                  >
                    开始检验
                  </el-button>
                  <div v-else class="validation-success">
                    <el-icon color="#67c23a"><Check /></el-icon>
                    <span style="color: #67c23a; margin-left: 5px;">检验成功！</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据匹配">
                <div class="data-match-container">
                  <el-select
                    v-model="templateForm.dataMatchType"
                    placeholder="请选择共有字段"
                    style="width: 120px; margin-right: 10px;"
                  >
                    <el-option
                      v-for="option in dataMatchOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                  <el-button
                    v-if="templateForm.dataMatchStatus === 'pending'"
                    type="primary"
                    size="small"
                    @click="startDataMatch"
                  >
                    开始执行
                  </el-button>
                  <div v-else class="validation-success">
                    <span style="color: #67c23a;">检验成功！</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 关联规则 -->
        <div class="form-section">
          <el-form-item label="关联规则">
            <div class="rules-container">
              <div
                v-for="rule in templateForm.relationRules"
                :key="rule.id"
                class="rule-item"
              >
                <el-select v-model="rule.rule" style="width: 200px;">
                  <el-option
                    v-for="option in relationRuleOptions"
                    :key="option"
                    :label="option"
                    :value="option"
                  />
                </el-select>
                <el-link
                  type="primary"
                  style="margin-left: 10px;"
                  @click="removeRelationRule(rule.id)"
                >
                  删除
                </el-link>
              </div>
              <el-link type="primary" @click="addRelationRule">添加规则</el-link>
            </div>
          </el-form-item>
        </div>

        <!-- 数据清洗规则设置 -->
        <div class="form-section">
          <el-form-item label="数据清洗规则设置">
            <div class="rules-container">
              <div
                v-for="rule in templateForm.cleaningRules"
                :key="rule.id"
                class="rule-item"
              >
                <el-select v-model="rule.rule" style="width: 200px;">
                  <el-option
                    v-for="option in cleaningRuleOptions"
                    :key="option"
                    :label="option"
                    :value="option"
                  />
                </el-select>
                <el-link
                  type="primary"
                  style="margin-left: 10px;"
                  @click="removeCleaningRule(rule.id)"
                >
                  删除
                </el-link>
              </div>
              <el-link type="primary" @click="addCleaningRule">添加规则</el-link>
            </div>
          </el-form-item>
        </div>

        <!-- 数据源配置 -->
        <div class="form-section">
          <div
            v-for="(dataSource, index) in templateForm.dataSources"
            :key="dataSource.id"
            class="data-source-item"
          >
            <div class="data-source-header">
              <span class="data-source-title">数据源 {{ index + 1 }}</span>
              <el-button
                v-if="templateForm.dataSources.length > 1"
                type="danger"
                size="small"
                :icon="Close"
                circle
                @click="removeDataSource(dataSource.id)"
              />
            </div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="选择算法" required>
                  <el-select v-model="dataSource.algorithm" placeholder="算法1" style="width: 100%">
                    <el-option
                      v-for="option in algorithmOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="数据获取源配置" required>
                  <el-select v-model="dataSource.dataSource" style="width: 100%">
                    <el-option
                      v-for="option in dataSourceOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="服务器地址">
                  <el-input v-model="dataSource.serverAddress" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="端口">
                  <el-input v-model="dataSource.port" placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="获取时间范围" required>
                  <div class="time-range-container">
                    <el-date-picker
                      v-model="dataSource.startTime"
                      type="datetime"
                      placeholder="开始时间"
                      format="YYYY-MM-DD HH:mm:ss"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      class="time-picker"
                    />
                    <span class="time-separator">至</span>
                    <el-date-picker
                      v-model="dataSource.endTime"
                      type="datetime"
                      placeholder="结束时间"
                      format="YYYY-MM-DD HH:mm:ss"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      class="time-picker"
                    />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="获取数据类型" required>
                  <el-select
                    v-model="dataSource.dataTypes"
                    multiple
                    placeholder="请选择数据类型"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="option in dataTypeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="获取数据量" required>
                  <el-input v-model="dataSource.dataAmount" placeholder="100000" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 添加数据源按钮 -->
          <div class="add-data-source">
            <el-button type="primary" :icon="Plus" @click="addDataSource">
              添加数据源
            </el-button>
          </div>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelCreate">取消</el-button>
          <el-button type="primary" @click="saveTemplate">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 抽查模板历史记录对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      title="抽查模板历史记录"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-table
        :data="currentTemplateHistory"
        style="width: 100%"
        @selection-change="(selection) => selectedHistoryTemplate = selection[0]"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="序号" label="序号" width="80" align="center" />
        <el-table-column prop="创建时间" label="创建时间" width="180" />
        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewHistoryTemplate(row)"
            >
              查看
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="useHistoryTemplate(row)"
            >
              使用
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelHistory">取消</el-button>
          <el-button type="primary" @click="confirmUseHistory">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看模板详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="查看模板详情"
      width="900px"
      :close-on-click-modal="false"
    >
      <div v-if="currentViewTemplate" class="template-detail-view">
        <!-- 基本信息 -->
        <div class="form-section">
          <h4 class="section-title">基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>模板名称：</label>
                <span>{{ currentViewTemplate.templateName }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>模板分类：</label>
                <el-tag :type="getTagType(currentViewTemplate.category)">
                  {{ currentViewTemplate.category }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>创建时间：</label>
                <span>{{ currentViewTemplate.createTime }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>创建者：</label>
                <span>{{ currentViewTemplate.creator }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="detail-item">
                <label>模板描述：</label>
                <span>{{ currentViewTemplate.description }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 有效性检验 -->
        <div class="form-section">
          <h4 class="section-title">有效性检验</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <label>有效性检验：</label>
                <div class="validation-success">
                  <el-icon color="#67c23a"><Check /></el-icon>
                  <span style="color: #67c23a; margin-left: 5px;">检验成功！</span>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>数据匹配：</label>
                <span style="color: #67c23a;">时间 - 检验成功！</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 关联规则 -->
        <div class="form-section">
          <h4 class="section-title">关联规则</h4>
          <div class="rules-display">
            <div class="rule-item-display">
              <el-tag type="info" size="small">主键-外键关系检查</el-tag>
            </div>
            <div class="rule-item-display">
              <el-tag type="info" size="small">业务逻辑一致性检查</el-tag>
            </div>
            <div class="rule-item-display">
              <el-tag type="info" size="small">交叉验证检查</el-tag>
            </div>
            <div class="rule-item-display">
              <el-tag type="info" size="small">时间序列一致性检查</el-tag>
            </div>
            <div class="rule-item-display">
              <el-tag type="info" size="small">唯一性约束检查</el-tag>
            </div>
          </div>
        </div>

        <!-- 数据清洗规则设置 -->
        <div class="form-section">
          <h4 class="section-title">数据清洗规则设置</h4>
          <div class="rules-display">
            <div class="rule-item-display">
              <el-tag type="warning" size="small">缺失值处理</el-tag>
            </div>
            <div class="rule-item-display">
              <el-tag type="warning" size="small">重复记录处理</el-tag>
            </div>
            <div class="rule-item-display">
              <el-tag type="warning" size="small">格式标准化</el-tag>
            </div>
            <div class="rule-item-display">
              <el-tag type="warning" size="small">异常值处理</el-tag>
            </div>
            <div class="rule-item-display">
              <el-tag type="warning" size="small">数据类型转换</el-tag>
            </div>
            <div class="rule-item-display">
              <el-tag type="warning" size="small">拼写纠正</el-tag>
            </div>
          </div>
        </div>

        <!-- 数据源配置 -->
        <div class="form-section">
          <h4 class="section-title">数据源配置</h4>
          <div class="data-source-display">
            <div class="data-source-item-display">
              <div class="data-source-header">
                <span class="data-source-title">数据源 1</span>
              </div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <label>选择算法：</label>
                    <span>算法1</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label>数据获取源配置：</label>
                    <span>台川区企业所得税表</span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <label>服务器地址：</label>
                    <span>192.168.1.100</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label>端口：</label>
                    <span>3306</span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <div class="detail-item">
                    <label>获取时间范围：</label>
                    <span>2024-04-26 13:00:21 至 2024-04-26 13:00:21</span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <label>获取数据类型：</label>
                    <div class="data-types">
                      <el-tag size="small" style="margin-right: 8px;">日报</el-tag>
                      <el-tag size="small">季报</el-tag>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label>获取数据量：</label>
                    <span>100000</span>
                  </div>
                </el-col>
              </el-row>
            </div>

            <div class="data-source-item-display">
              <div class="data-source-header">
                <span class="data-source-title">数据源 2</span>
              </div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <label>选择算法：</label>
                    <span>算法2</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label>数据获取源配置：</label>
                    <span>江津区企业所得税表</span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <label>服务器地址：</label>
                    <span>192.168.1.101</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label>端口：</label>
                    <span>3306</span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <div class="detail-item">
                    <label>获取时间范围：</label>
                    <span>2024-04-26 13:00:21 至 2024-04-26 13:00:21</span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <label>获取数据类型：</label>
                    <div class="data-types">
                      <el-tag size="small" style="margin-right: 8px;">日报</el-tag>
                      <el-tag size="small">季报</el-tag>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label>获取数据量：</label>
                    <span>100000</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleEdit(currentViewTemplate)">编辑</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<route>
{
  meta: {
    title: '配置抽查模板'
  }
}
</route>

<style scoped lang="scss">
.config-template {
  .action-buttons {
    margin-bottom: 16px;
    padding: 12px 0;
    text-align: right;
    border-bottom: 1px solid #e9ecef;

    .el-button {
      margin-left: 8px;

      &:first-child {
        margin-left: 0;
      }
    }
  }

  .search {
    margin-bottom: 16px;
    padding: 16px 0;
    overflow: hidden !important; // 强制移除滚动条
    max-height: none !important; // 移除高度限制

    .search-form {
      overflow: hidden !important; // 确保表单内容不产生滚动条
      max-height: none !important;
    }

    :deep(.el-form) {
      overflow: hidden !important; // 移除表单滚动条
      max-height: none !important;

      .el-form-item {
        margin-bottom: 12px;

        .el-form-item__label {
          font-size: 13px;
          color: #606266;
        }

        .el-input, .el-select {
          .el-input__wrapper {
            height: 32px;
          }
        }
      }

      .el-form-item__content {
        .el-button {
          height: 32px;
          padding: 8px 16px;
          font-size: 13px;
        }
      }
    }
  }

  // 确保整个页面区域没有滚动条
  :deep(.block-expand) {
    overflow: hidden !important;
    max-height: none !important;
  }

  // 全局移除滚动条 - 更彻底的方法
  :deep(*) {
    &::-webkit-scrollbar {
      display: none !important;
      width: 0 !important;
      height: 0 !important;
    }

    &::-webkit-scrollbar-track {
      display: none !important;
    }

    &::-webkit-scrollbar-thumb {
      display: none !important;
    }

    scrollbar-width: none !important; // Firefox
    -ms-overflow-style: none !important; // IE
  }

  // 移除任何可能的滚动条
  :deep(.el-scrollbar) {
    overflow: hidden !important;

    .el-scrollbar__wrap {
      overflow: hidden !important;
      scrollbar-width: none !important;
      -ms-overflow-style: none !important;

      &::-webkit-scrollbar {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
      }
    }

    .el-scrollbar__bar {
      display: none !important;
    }
  }

  // 针对Block组件的特殊处理
  :deep(.block) {
    overflow: hidden !important;

    .block-header,
    .block-content,
    .block-expand {
      overflow: hidden !important;

      &::-webkit-scrollbar {
        display: none !important;
      }
    }
  }

  // 针对搜索区域的特殊处理
  :deep(.block-expand) {
    overflow: hidden !important;
    max-height: none !important;

    * {
      overflow: hidden !important;
      max-height: none !important;

      &::-webkit-scrollbar {
        display: none !important;
      }
    }
  }

  // 新建模板对话框样式
  :deep(.el-dialog) {
    .template-form {
      .form-section {
        margin-bottom: 24px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;

        .el-form-item {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .validation-container,
      .data-match-container {
        display: flex;
        align-items: center;

        .validation-success {
          display: flex;
          align-items: center;
        }
      }

      .rules-container {
        .rule-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .data-source-item {
        margin-bottom: 24px;
        padding: 20px;
        border: 1px solid #dcdfe6;
        border-radius: 8px;
        background: #ffffff;
        position: relative;

        &:last-child {
          margin-bottom: 0;
        }

        .data-source-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          padding-bottom: 12px;
          border-bottom: 1px solid #e9ecef;

          .data-source-title {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
          }
        }

        .time-range-container {
          display: flex;
          align-items: center;
          gap: 16px;
          flex-wrap: wrap;

          .time-picker {
            width: 220px;
            flex-shrink: 0;
          }

          .time-separator {
            color: #606266;
            font-weight: 500;
            white-space: nowrap;
            margin: 0 8px;
          }

          @media (max-width: 768px) {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;

            .time-picker {
              width: 100%;
            }

            .time-separator {
              align-self: center;
              margin: 0;
            }
          }
        }
      }

      .add-data-source {
        text-align: center;
        padding: 20px;
        border: 2px dashed #dcdfe6;
        border-radius: 8px;
        background: #fafafa;

        &:hover {
          border-color: #409eff;
          background: #f0f9ff;
        }
      }
    }

    .dialog-footer {
      text-align: right;
      padding-top: 16px;
      border-top: 1px solid #e9ecef;

      .el-button {
        margin-left: 12px;

        &:first-child {
          margin-left: 0;
        }
      }
    }
  }

  // 历史记录对话框样式
  :deep(.el-dialog) {
    .el-table {
      .el-button {
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  // 查看模板详情对话框样式
  .template-detail-view {
    .form-section {
      margin-bottom: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e9ecef;

      .section-title {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 8px;
      }

      .detail-item {
        margin-bottom: 16px;
        display: flex;
        align-items: center;

        label {
          font-weight: 500;
          color: #606266;
          margin-right: 12px;
          min-width: 120px;
          flex-shrink: 0;
        }

        span {
          color: #303133;
        }

        .validation-success {
          display: flex;
          align-items: center;
        }

        .data-types {
          display: flex;
          align-items: center;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      .rules-display {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .rule-item-display {
          margin-bottom: 8px;
        }
      }

      .data-source-display {
        .data-source-item-display {
          margin-bottom: 24px;
          padding: 16px;
          border: 1px solid #dcdfe6;
          border-radius: 8px;
          background: #ffffff;

          &:last-child {
            margin-bottom: 0;
          }

          .data-source-header {
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e9ecef;

            .data-source-title {
              font-weight: 600;
              color: #303133;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
</style>
