<script setup lang="ts" name="commonDataTypeManagement">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Star, Plus, Edit, Delete, Download, Upload, View, User, Clock, Refresh, DataAnalysis, Coin } from '@element-plus/icons-vue'

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const tableData = ref<any[]>([])
const filteredData = ref<any[]>([])
const selectedRows = ref<any[]>([])

// 搜索表单
const searchForm = ref({
  dataTypeName: '',
  category: '',
  importance: ''
})

const searchFormProp = ref([
  { label: '数据类型名称', prop: 'dataTypeName', type: 'text' },
  {
    label: '分类',
    prop: 'category',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '数据型', value: '数据型' },
      { label: '字符型', value: '字符型' },
      { label: '日期型', value: '日期型' },
      { label: '布尔型', value: '布尔型' },
      { label: '其他型', value: '其他型' }
    ]
  },
  {
    label: '重要性',
    prop: 'importance',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '高', value: '高' },
      { label: '中', value: '中' },
      { label: '低', value: '低' }
    ]
  }
])

// 表格配置
const tableRef = ref()
const tableHeight = ref(0)

// 表头配置
const columns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'tag', label: '标签', minWidth: 120 },
  { prop: 'dataTypeName', label: '数据类型名称', minWidth: 150 },
  { prop: 'category', label: '分类', minWidth: 100 },
  { prop: 'dataVolume', label: '数据量', minWidth: 100, sortable: true },
  { prop: 'permissions', label: '权限', minWidth: 150 },
  { prop: 'favorite', label: '收藏', minWidth: 120, sortable: true },
  { prop: 'importance', label: '重要性', minWidth: 100, sortable: true },
  { prop: 'remark', label: '备注', minWidth: 200 },
  { prop: 'action', label: '操作', width: 280, fixed: 'right' }
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 列表请求参数
const reqParams = reactive({
  dataTypeName: '',
  category: '',
  importance: '',
  skipCount: 0,
  maxResultCount: 10
})

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const currentRow = ref<any>(null)
const isViewMode = ref(false)

// 表单数据
const dialogForm = ref({
  dataTypeName: '',
  dataTypeDescription: '',
  category: '',
  storageRequirement: '',
  valueRange: '',
  defaultValue: '',
  constraints: '',
  permissions: [] as string[]
})

// 表单属性
const dialogFormProps = ref([
  { label: '数据类型名称', prop: 'dataTypeName', type: 'text', required: true },
  { label: '数据类型描述', prop: 'dataTypeDescription', type: 'textarea' },
  {
    label: '类型分类',
    prop: 'category',
    type: 'select',
    options: [
      { label: '数据型', value: '数据型' },
      { label: '字符型', value: '字符型' },
      { label: '日期型', value: '日期型' },
      { label: '布尔型', value: '布尔型' },
      { label: '其他型', value: '其他型' }
    ]
  },
  { label: '存储需求', prop: 'storageRequirement', type: 'text' },
  { label: '数值范围', prop: 'valueRange', type: 'text' },
  { label: '默认值', prop: 'defaultValue', type: 'text' },
  { label: '约束条件', prop: 'constraints', type: 'textarea' },
  {
    label: '权限',
    prop: 'permissions',
    type: 'checkbox',
    options: [
      { label: '访问', value: '访问' },
      { label: '编辑', value: '编辑' },
      { label: '删除', value: '删除' }
    ]
  }
])

// 表单验证规则
const dialogFormRules = {
  dataTypeName: [{ required: true, message: '请输入数据类型名称', trigger: 'blur' }]
}

// 本地存储键
const STORAGE_KEY = 'commonDataTypeManagement'

// 统计数据
const statisticsData = ref<any>({})

// 生成统计数据
const generateStatisticsData = (row: any) => {
  const creators = ['张三', '李四', '王五', '赵六', '钱七', '孙八']
  const dataSources = ['数据库A', '数据库B', '外部API', '文件导入', '手动录入']
  const updateFrequencies = ['实时', '每小时', '每日', '每周', '每月']

  return {
    creator: creators[Math.floor(Math.random() * creators.length)],
    createTime: row.createTime,
    lastModifyDate: new Date(new Date(row.createTime).getTime() + Math.floor(Math.random() * 7) * 24 * 60 * 60 * 1000).toISOString(),
    dataVolume: row.dataVolume,
    averageValue: row.category === '数据型' ? (Math.random() * 1000).toFixed(2) : 'N/A',
    medianValue: row.category === '数据型' ? (Math.random() * 1000).toFixed(2) : 'N/A',
    modeValue: row.category === '数据型' ? Math.floor(Math.random() * 100) : row.category === '字符型' ? '常用值' : 'N/A',
    variance: row.category === '数据型' ? (Math.random() * 100).toFixed(2) : 'N/A',
    dataSource: dataSources[Math.floor(Math.random() * dataSources.length)],
    updateFrequency: updateFrequencies[Math.floor(Math.random() * updateFrequencies.length)]
  }
}

// 返回上级页面
const handleGoBack = () => {
  router.push('/reportIntegrationSpotCheck')
}

// 块高度变化事件
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 120
}

// 生成模拟数据
const generateMockData = () => {
  const categories = ['数据型', '字符型', '日期型', '布尔型', '其他型']
  const importanceLevels = ['高', '中', '低']
  const tagColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
  const dataTypeNames = [
    'INT', 'VARCHAR', 'DECIMAL', 'DATE', 'BOOLEAN', 'TEXT', 'FLOAT', 'TIMESTAMP', 'CHAR', 'BLOB'
  ]

  const mockData = []
  const now = new Date()

  for (let i = 1; i <= 50; i++) {
    const category = categories[Math.floor(Math.random() * categories.length)]
    const importance = importanceLevels[Math.floor(Math.random() * importanceLevels.length)]
    const dataTypeName = dataTypeNames[Math.floor(Math.random() * dataTypeNames.length)]

    // 创建时间：从现在往前推随机天数
    const createTime = new Date(now.getTime() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000)

    mockData.push({
      id: i,
      index: i,
      tag: {
        text: `标签${i}`,
        color: tagColors[Math.floor(Math.random() * tagColors.length)]
      },
      dataTypeName: dataTypeName,
      category: category,
      dataVolume: Math.floor(Math.random() * 10000) + 1000,
      permissions: ['访问', '编辑', '删除'].slice(0, Math.floor(Math.random() * 3) + 1),
      favorite: Math.floor(Math.random() * 5) + 1,
      importance: importance,
      remark: `这是${dataTypeName}类型的备注信息`,
      dataTypeDescription: `${dataTypeName}数据类型的详细描述`,
      storageRequirement: `${Math.floor(Math.random() * 64) + 1}字节`,
      valueRange: category === '数据型' ? '0-999999' : category === '字符型' ? '1-255字符' : '标准范围',
      defaultValue: category === '数据型' ? '0' : category === '字符型' ? '空字符串' : '默认值',
      constraints: '非空约束，唯一性约束',
      createTime: createTime.toISOString()
    })
  }

  // 按创建时间降序排序（最新的在前面）
  return mockData.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
}

// 初始化数据
const initData = () => {
  const savedData = localStorage.getItem(STORAGE_KEY)
  if (savedData) {
    tableData.value = JSON.parse(savedData)
    // 确保已保存的数据也按创建时间排序
    tableData.value.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
  } else {
    tableData.value = generateMockData()
    saveToLocalStorage()
  }
  applyFilters()
}

// 保存到本地存储
const saveToLocalStorage = () => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(tableData.value))
}

// 应用筛选
const applyFilters = () => {
  let filtered = [...tableData.value]

  if (searchForm.value.dataTypeName) {
    filtered = filtered.filter(item =>
      item.dataTypeName.toLowerCase().includes(searchForm.value.dataTypeName.toLowerCase())
    )
  }

  if (searchForm.value.category) {
    filtered = filtered.filter(item => item.category === searchForm.value.category)
  }

  if (searchForm.value.importance) {
    filtered = filtered.filter(item => item.importance === searchForm.value.importance)
  }

  // 按创建时间降序排序
  filtered.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())

  filteredData.value = filtered
  pagination.total = filtered.length
  pagination.page = 1

  // 清空选中项
  selectedRows.value = []
}

// 获取当前页数据
const getCurrentPageData = () => {
  let data = [...filteredData.value]

  // 应用排序
  if (sortConfig.value.prop && sortConfig.value.order) {
    data = getSortedData(data)
  }

  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  pagination.total = data.length

  return data.slice(start, end).map((item, index) => ({
    ...item,
    index: start + index + 1
  }))
}

// 搜索
const onSearch = () => {
  pagination.page = 1
  reqParams.skipCount = 0
  reqParams.maxResultCount = pagination.size
  // 其他查询参数
  reqParams.dataTypeName = searchForm.value.dataTypeName
  reqParams.category = searchForm.value.category
  reqParams.importance = searchForm.value.importance
  applyFilters()
}

// 重置搜索
const onReset = () => {
  searchForm.value = {
    dataTypeName: '',
    category: '',
    importance: ''
  }
  reqParams.dataTypeName = ''
  reqParams.category = ''
  reqParams.importance = ''
  applyFilters()
}

// 新增
const onClickAdd = () => {
  currentRow.value = null
  isViewMode.value = false
  dialogForm.value = {
    dataTypeName: '',
    dataTypeDescription: '',
    category: '',
    storageRequirement: '',
    valueRange: '',
    defaultValue: '',
    constraints: '',
    permissions: []
  }
  showDialogForm.value = true
}

// 查看详情
const onView = (row: any) => {
  currentRow.value = row
  statisticsData.value = generateStatisticsData(row)
  dialogForm.value = {
    dataTypeName: row.dataTypeName,
    dataTypeDescription: row.dataTypeDescription,
    category: row.category,
    storageRequirement: row.storageRequirement,
    valueRange: row.valueRange,
    defaultValue: row.defaultValue,
    constraints: row.constraints,
    permissions: [...row.permissions]
  }
  isViewMode.value = true
  showDialogForm.value = true
}

// 编辑
const onEdit = (row: any) => {
  currentRow.value = row
  dialogForm.value = {
    dataTypeName: row.dataTypeName,
    dataTypeDescription: row.dataTypeDescription,
    category: row.category,
    storageRequirement: row.storageRequirement,
    valueRange: row.valueRange,
    defaultValue: row.defaultValue,
    constraints: row.constraints,
    permissions: [...row.permissions]
  }
  isViewMode.value = false
  showDialogForm.value = true
}

// 删除
const onDelete = (row: any) => {
  ElMessageBox.confirm('确认删除该数据类型吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      saveToLocalStorage()
      applyFilters()
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 批量删除
const onBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }
  
  ElMessageBox.confirm(`确认删除选中的 ${selectedRows.value.length} 条数据吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const selectedIds = selectedRows.value.map(row => row.id)
    tableData.value = tableData.value.filter(item => !selectedIds.includes(item.id))
    saveToLocalStorage()
    applyFilters()
    selectedRows.value = []
    ElMessage.success('批量删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 表单提交
const onDialogConfirm = () => {
  dialogFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true
      
      if (currentRow.value) {
        // 编辑
        const index = tableData.value.findIndex(item => item.id === currentRow.value.id)
        if (index > -1) {
          Object.assign(tableData.value[index], {
            dataTypeName: dialogForm.value.dataTypeName,
            dataTypeDescription: dialogForm.value.dataTypeDescription,
            category: dialogForm.value.category,
            storageRequirement: dialogForm.value.storageRequirement,
            valueRange: dialogForm.value.valueRange,
            defaultValue: dialogForm.value.defaultValue,
            constraints: dialogForm.value.constraints,
            permissions: [...dialogForm.value.permissions],
            remark: `这是${dialogForm.value.dataTypeName}类型的备注信息`
          })
        }
        ElMessage.success('编辑成功')
      } else {
        // 新增
        const newId = Math.max(...tableData.value.map(item => item.id)) + 1
        const tagColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']

        const newItem = {
          id: newId,
          index: newId,
          tag: {
            text: `标签${newId}`,
            color: tagColors[Math.floor(Math.random() * tagColors.length)]
          },
          dataTypeName: dialogForm.value.dataTypeName,
          category: dialogForm.value.category,
          dataVolume: Math.floor(Math.random() * 10000) + 1000,
          permissions: [...dialogForm.value.permissions],
          favorite: Math.floor(Math.random() * 5) + 1,
          importance: ['高', '中', '低'][Math.floor(Math.random() * 3)],
          remark: `这是${dialogForm.value.dataTypeName}类型的备注信息`,
          dataTypeDescription: dialogForm.value.dataTypeDescription,
          storageRequirement: dialogForm.value.storageRequirement,
          valueRange: dialogForm.value.valueRange,
          defaultValue: dialogForm.value.defaultValue,
          constraints: dialogForm.value.constraints,
          createTime: new Date().toISOString()
        }

        // 将新项添加到数组开头（最新的在前面）
        tableData.value.unshift(newItem)
        ElMessage.success('新增成功')
      }

      saveToLocalStorage()
      applyFilters()
      showDialogForm.value = false
      loading.value = false
    }
  })
}

// 分页事件
const onPaginationChange = (val: number, type: string) => {
  if (type === 'page') {
    pagination.page = val
    reqParams.skipCount = (val - 1) * pagination.size
  } else {
    pagination.size = val
    pagination.page = 1
    reqParams.maxResultCount = pagination.size
    reqParams.skipCount = 0
  }
}

// 排序配置
const sortConfig = ref({
  prop: '',
  order: ''
})

// 表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 排序变化处理
const handleSortChange = ({ prop, order }: any) => {
  sortConfig.value = { prop, order }
}

// 获取排序后的数据
const getSortedData = (data: any[]) => {
  if (!sortConfig.value.prop || !sortConfig.value.order) {
    return data
  }

  return [...data].sort((a, b) => {
    const aVal = a[sortConfig.value.prop]
    const bVal = b[sortConfig.value.prop]

    if (sortConfig.value.order === 'ascending') {
      return aVal > bVal ? 1 : -1
    } else {
      return aVal < bVal ? 1 : -1
    }
  })
}



// 组件挂载
onMounted(() => {
  initData()
})
</script>

<template>
  <div class="common-data-type-management">
    <Block
      title="常用数据类型管理"
      :enable-expand-content="true"
      :enableBackButton="false"
      :enable-fixed-height="true"
      :enable-close-button="false"
      @content-expand="() => {}"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button 
          size="small" 
          type="default" 
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
        
        <el-button size="small" type="primary" @click="onClickAdd">
          <el-icon style="margin-right: 4px">
            <Plus />
          </el-icon>
          新增数据类型
        </el-button>

        <el-button
          size="small"
          type="danger"
          @click="onBatchDelete"
          :disabled="selectedRows.length === 0"
          style="margin-left: 8px"
        >
          批量删除 ({{ selectedRows.length }})
        </el-button>
      </template>

      <template #expand>
        <!-- 搜索区域 -->
        <div class="search" v-loading="searchLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="4"
            :label-width="100"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
            @reset="onReset"
          />
        </div>
      </template>

      <!-- 数据表格 -->
      <TableV2
        ref="tableRef"
        :columns="columns"
        :defaultTableData="getCurrentPageData()"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :enable-index="false"
        :height="tableHeight"
        :loading="loading"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <!-- 序号列 -->
        <template #index="{ row }">
          {{ row.index }}
        </template>

        <!-- 标签列 -->
        <template #tag="{ row }">
          <el-tag :color="row.tag.color" style="color: white; border: none;">
            {{ row.tag.text }}
          </el-tag>
        </template>

        <!-- 数据量列 -->
        <template #dataVolume="{ row }">
          {{ row.dataVolume.toLocaleString() }}
        </template>

        <!-- 权限列 -->
        <template #permissions="{ row }">
          <el-tag
            v-for="permission in row.permissions"
            :key="permission"
            size="small"
            style="margin-right: 4px;"
            :type="permission === '访问' ? 'success' : permission === '编辑' ? 'warning' : 'danger'"
          >
            {{ permission }}
          </el-tag>
        </template>

        <!-- 收藏列 -->
        <template #favorite="{ row }">
          <div class="star-rating">
            <el-icon
              v-for="i in 5"
              :key="i"
              :class="i <= row.favorite ? 'star-filled' : 'star-empty'"
              size="16"
            >
              <Star />
            </el-icon>
          </div>
        </template>

        <!-- 重要性列 -->
        <template #importance="{ row }">
          <el-tag
            :type="row.importance === '高' ? 'danger' : row.importance === '中' ? 'warning' : 'info'"
            size="small"
          >
            {{ row.importance }}
          </el-tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ row }">
          <div class="action-buttons">
            <el-button
              size="small"
              type="info"
              @click="onView(row)"
            >
              <el-icon style="margin-right: 2px">
                <View />
              </el-icon>
              查看
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="onEdit(row)"
            >
              <el-icon style="margin-right: 2px">
                <Edit />
              </el-icon>
              编辑
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="onDelete(row)"
            >
              <el-icon style="margin-right: 2px">
                <Delete />
              </el-icon>
              删除
            </el-button>
          </div>
        </template>
      </TableV2>

      <!-- 分页 -->
      <div class="pagination-container">
        <Pagination
          :total="pagination.total"
          :current-page="pagination.page"
          :page-size="pagination.size"
          @current-change="onPaginationChange($event, 'page')"
          @size-change="onPaginationChange($event, 'size')"
        />
      </div>
    </Block>

    <!-- 新增/编辑/查看对话框 -->
    <Dialog
      v-model="showDialogForm"
      :title="isViewMode ? '查看数据类型详情' : (currentRow ? '编辑数据类型' : '新增数据类型')"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      :width="isViewMode ? '900px' : '600px'"
      @closed="currentRow = null; isViewMode = false"
      @click-confirm="onDialogConfirm"
      :enable-confirm="!isViewMode"
      :close-on-click-modal="false"
      :max-height="isViewMode ? '80vh' : 'auto'"
    >
      <div v-if="isViewMode" class="detail-view">
        <div class="detail-scroll-container">
          <!-- 基本信息 -->
          <div class="detail-section">
            <h3 class="section-title">基本信息</h3>
            <Form
              ref="dialogFormRef"
              v-model="dialogForm"
              :props="dialogFormProps.map(prop => ({ ...prop, disabled: true }))"
              :rules="{}"
              :enable-button="false"
              :column-count="1"
              :label-width="120"
            />
          </div>

        <!-- 统计数据 -->
        <div class="detail-section">
          <h3 class="section-title">统计数据</h3>

          <!-- 基础信息卡片 -->
          <el-row :gutter="16" style="margin-bottom: 16px; margin-top: 10px;">
            <el-col :span="8">
              <el-card class="stat-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon creator-icon">
                    <el-icon size="24"><User /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">创建者</div>
                    <div class="stat-value">{{ statisticsData.creator }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="stat-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon time-icon">
                    <el-icon size="24"><Clock /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">创建时间</div>
                    <div class="stat-value">{{ new Date(statisticsData.createTime).toLocaleDateString('zh-CN') }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="stat-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon update-icon">
                    <el-icon size="24"><Refresh /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">最后修改</div>
                    <div class="stat-value">{{ new Date(statisticsData.lastModifyDate).toLocaleDateString('zh-CN') }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 数据统计卡片 -->
          <el-row :gutter="16" style="margin-bottom: 16px; margin-top: 10px;">
            <el-col :span="12">
              <el-card class="stat-card large-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon data-icon">
                    <el-icon size="28"><DataAnalysis /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">数据量</div>
                    <div class="stat-value large-value">{{ statisticsData.dataVolume?.toLocaleString() }}</div>
                    <div class="stat-desc">条记录</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="stat-card large-card" shadow="hover">
                <div class="stat-content">
                  <div class="stat-icon source-icon">
                    <el-icon size="28"><Coin /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">数据来源</div>
                    <div class="stat-value large-value">{{ statisticsData.dataSource }}</div>
                    <div class="stat-desc">{{ statisticsData.updateFrequency }}更新</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 数值统计 -->
          <div v-if="dialogForm.category === '数据型'">
            <h4 class="sub-title">数值统计</h4>
            <el-row :gutter="16" style="margin-bottom: 16px; margin-top: 10px;">
              <el-col :span="6">
                <el-card class="stat-card mini-card" shadow="hover">
                  <div class="mini-stat">
                    <div class="mini-label">平均值</div>
                    <div class="mini-value">{{ statisticsData.averageValue }}</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card mini-card" shadow="hover">
                  <div class="mini-stat">
                    <div class="mini-label">中位数</div>
                    <div class="mini-value">{{ statisticsData.medianValue }}</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card mini-card" shadow="hover">
                  <div class="mini-stat">
                    <div class="mini-label">众数</div>
                    <div class="mini-value">{{ statisticsData.modeValue }}</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card mini-card" shadow="hover">
                  <div class="mini-stat">
                    <div class="mini-label">方差</div>
                    <div class="mini-value">{{ statisticsData.variance }}</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <div v-else>
            <el-alert
              title="数值统计"
              description="当前数据类型不支持数值统计分析"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </div>
        </div>
      </div>

      <div v-else>
        <Form
          ref="dialogFormRef"
          v-model="dialogForm"
          :props="dialogFormProps"
          :rules="dialogFormRules"
          :enable-button="false"
          :column-count="1"
          :label-width="120"
        />
      </div>
    </Dialog>
  </div>
</template>

<route>
{
  meta: {
    title: '常用数据类型管理'
  }
}
</route>

<style scoped lang="scss">
.common-data-type-management {
  .search {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }

  .table-container {
    margin-bottom: 16px;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0;
  }

  .star-rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;

    .star-filled {
      color: #f7ba2a;
      transition: color 0.3s ease;
    }

    .star-empty {
      color: #dcdfe6;
      transition: color 0.3s ease;
    }
  }

  // 响应式表格样式
  :deep(.el-table) {
    font-size: 14px;

    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 500;
          border-bottom: 2px solid #e4e7ed;
          padding: 12px 0;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          transition: background-color 0.3s ease;

          &:hover {
            background-color: #f5f7fa;
          }

          td {
            padding: 12px 0;
            border-bottom: 1px solid #ebeef5;
          }
        }
      }
    }

    // 响应式处理
    @media (max-width: 1200px) {
      .el-table__header th,
      .el-table__body td {
        padding: 8px 4px;
        font-size: 13px;
      }
    }

    @media (max-width: 768px) {
      .el-table__header th,
      .el-table__body td {
        padding: 6px 2px;
        font-size: 12px;
      }
    }
  }

  // 标签样式优化
  :deep(.el-tag) {
    margin-right: 4px;
    margin-bottom: 2px;
    border-radius: 4px;
    font-size: 12px;
    padding: 0 8px;
    height: 24px;
    line-height: 22px;
    transition: all 0.3s ease;

    &.el-tag--success {
      background-color: #f0f9ff;
      border-color: #b3d8ff;
      color: #409eff;
    }

    &.el-tag--warning {
      background-color: #fdf6ec;
      border-color: #f5dab1;
      color: #e6a23c;
    }

    &.el-tag--danger {
      background-color: #fef0f0;
      border-color: #fbc4c4;
      color: #f56c6c;
    }

    &.el-tag--info {
      background-color: #f4f4f5;
      border-color: #d3d4d6;
      color: #909399;
    }
  }

  // 按钮样式优化
  :deep(.el-button) {
    border-radius: 4px;
    transition: all 0.3s ease;
    font-weight: 400;

    &.el-button--small {
      padding: 7px 15px;
      font-size: 12px;
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  // 对话框样式优化
  :deep(.el-dialog) {
    border-radius: 8px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);

    .el-dialog__header {
      padding: 20px 20px 10px;
      border-bottom: 1px solid #ebeef5;

      .el-dialog__title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 10px 20px 20px;
      border-top: 1px solid #ebeef5;
    }
  }

  // 表单样式优化
  :deep(.el-form) {
    .el-form-item {
      margin-bottom: 18px;

      .el-form-item__label {
        color: #606266;
        font-weight: 500;
        line-height: 32px;
      }

      .el-form-item__content {
        .el-input,
        .el-select,
        .el-textarea {
          .el-input__inner,
          .el-textarea__inner {
            border-radius: 4px;
            transition: all 0.3s ease;

            &:focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
          }
        }

        .el-checkbox-group {
          .el-checkbox {
            margin-right: 16px;
            margin-bottom: 8px;

            .el-checkbox__label {
              color: #606266;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  // 详情视图样式
  .detail-view {
    .detail-scroll-container {
      max-height: 70vh;
      overflow-y: auto;
      padding-right: 8px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }

    .detail-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 3px solid #409eff;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -3px;
          left: 0;
          width: 60px;
          height: 3px;
          background: linear-gradient(90deg, #409eff, #67c23a);
          border-radius: 2px;
        }
      }

      .sub-title {
        font-size: 16px;
        font-weight: 500;
        color: #606266;
        margin: 20px 0 16px 0;
        padding-left: 12px;
        border-left: 4px solid #e6a23c;
      }
    }

    // 统计卡片样式
    .stat-card {
      border-radius: 12px;
      border: none;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
      }

      .stat-content {
        display: flex;
        align-items: center;
        padding: 8px;

        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          &.creator-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
          }

          &.time-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
          }

          &.update-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
          }

          &.data-icon {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
          }

          &.source-icon {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
          }
        }

        .stat-info {
          flex: 1;

          .stat-label {
            font-size: 14px;
            color: #909399;
            margin-bottom: 4px;
          }

          .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: #303133;

            &.large-value {
              font-size: 24px;
              font-weight: 700;
            }
          }

          .stat-desc {
            font-size: 12px;
            color: #c0c4cc;
            margin-top: 2px;
          }
        }
      }

      &.large-card .stat-content {
        padding: 16px;

        .stat-icon {
          width: 80px;
          height: 80px;
        }
      }

      &.mini-card {
        .mini-stat {
          text-align: center;
          padding: 16px 8px;

          .mini-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 8px;
          }

          .mini-value {
            font-size: 20px;
            font-weight: 600;
            color: #409eff;
          }
        }
      }
    }
  }

  // 响应式布局
  @media (max-width: 768px) {
    .search {
      padding: 12px;
      margin-bottom: 12px;
    }

    .table-container {
      margin-bottom: 12px;
    }

    .pagination-container {
      padding: 12px 0;
      justify-content: center;
    }

    :deep(.el-button) {
      &.el-button--small {
        padding: 5px 10px;
        font-size: 11px;
      }
    }
  }

  @media (max-width: 480px) {
    :deep(.el-table) {
      .el-table__header th,
      .el-table__body td {
        &:nth-child(n+6) {
          display: none;
        }
      }
    }

    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto !important;
    }
  }
}
</style>
