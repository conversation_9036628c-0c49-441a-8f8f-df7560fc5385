import type { DuplicateRule, PaginationConfig, DuplicateSearchForm, DuplicateStatisticsRule } from './types'
import { STORAGE_KEYS } from './types'
import { defaultMockData } from './mockData'

/**
 * 重复判断规则本地存储管理类
 */
export class DuplicateRuleStorage {

  /**
   * 获取重复判断规则列表
   */
  static getRuleList(): DuplicateRule[] {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.DUPLICATE_RULE_LIST)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，使用默认模拟数据并保存
      this.saveRuleList(defaultMockData)
      return defaultMockData
    } catch (error) {
      console.error('获取重复判断规则列表失败:', error)
      return defaultMockData
    }
  }

  /**
   * 保存重复判断规则列表
   */
  static saveRuleList(ruleList: DuplicateRule[]): void {
    try {
      localStorage.setItem(STORAGE_KEYS.DUPLICATE_RULE_LIST, JSON.stringify(ruleList))
    } catch (error) {
      console.error('保存重复判断规则列表失败:', error)
    }
  }

  /**
   * 添加重复判断规则
   */
  static addRule(rule: Omit<DuplicateRule, 'id' | 'createTime'>): DuplicateRule {
    const ruleList = this.getRuleList()
    const newRule: DuplicateRule = {
      ...rule,
      id: `duplicate_rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createTime: new Date().toISOString().replace('T', ' ').split('.')[0]
    }

    ruleList.unshift(newRule)
    this.saveRuleList(ruleList)
    return newRule
  }

  /**
   * 更新重复判断规则
   */
  static updateRule(id: string, updates: Partial<DuplicateRule>): boolean {
    try {
      const ruleList = this.getRuleList()
      const index = ruleList.findIndex(rule => rule.id === id)

      if (index !== -1) {
        ruleList[index] = {
          ...ruleList[index],
          ...updates
        }
        this.saveRuleList(ruleList)
        return true
      }
      return false
    } catch (error) {
      console.error('更新重复判断规则失败:', error)
      return false
    }
  }

  /**
   * 删除重复判断规则
   */
  static deleteRule(id: string): boolean {
    try {
      const ruleList = this.getRuleList()
      const filteredList = ruleList.filter(rule => rule.id !== id)

      if (filteredList.length !== ruleList.length) {
        this.saveRuleList(filteredList)
        return true
      }
      return false
    } catch (error) {
      console.error('删除重复判断规则失败:', error)
      return false
    }
  }

  /**
   * 根据ID获取重复判断规则
   */
  static getRuleById(id: string): DuplicateRule | null {
    try {
      const ruleList = this.getRuleList()
      return ruleList.find(rule => rule.id === id) || null
    } catch (error) {
      console.error('获取重复判断规则失败:', error)
      return null
    }
  }

  /**
   * 搜索重复判断规则
   */
  static searchRules(searchForm: DuplicateSearchForm): DuplicateRule[] {
    try {
      const ruleList = this.getRuleList()
      return ruleList.filter(rule => {
        const reportATypeMatch = !searchForm.reportAType || rule.reportAType === searchForm.reportAType
        const reportAFieldMatch = !searchForm.reportAField || rule.reportAField.includes(searchForm.reportAField)
        const reportBTypeMatch = !searchForm.reportBType || rule.reportBType === searchForm.reportBType
        const reportBFieldMatch = !searchForm.reportBField || rule.reportBField.includes(searchForm.reportBField)
        const matchTypeMatch = !searchForm.matchType || rule.matchType === searchForm.matchType

        return reportATypeMatch && reportAFieldMatch && reportBTypeMatch && reportBFieldMatch && matchTypeMatch
      })
    } catch (error) {
      console.error('搜索重复判断规则失败:', error)
      return []
    }
  }

  /**
   * 获取分页配置
   */
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.DUPLICATE_RULE_PAGINATION)
      if (data) {
        return JSON.parse(data)
      }
      return { page: 1, size: 10, total: 0 }
    } catch (error) {
      console.error('获取分页配置失败:', error)
      return { page: 1, size: 10, total: 0 }
    }
  }

  /**
   * 保存分页配置
   */
  static savePaginationConfig(pagination: PaginationConfig): void {
    try {
      localStorage.setItem(STORAGE_KEYS.DUPLICATE_RULE_PAGINATION, JSON.stringify(pagination))
    } catch (error) {
      console.error('保存分页配置失败:', error)
    }
  }

  /**
   * 获取搜索表单数据
   */
  static getSearchForm(): DuplicateSearchForm {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.DUPLICATE_RULE_SEARCH)
      if (data) {
        const parsed = JSON.parse(data)
        return { reportAType: '', reportAField: '', reportBType: '', reportBField: '', matchType: '', ...parsed }
      }
      return { reportAType: '', reportAField: '', reportBType: '', reportBField: '', matchType: '' }
    } catch (error) {
      console.error('获取搜索表单失败:', error)
      return { reportAType: '', reportAField: '', reportBType: '', reportBField: '', matchType: '' }
    }
  }

  /**
   * 保存搜索表单数据
   */
  static saveSearchForm(searchForm: DuplicateSearchForm): void {
    try {
      localStorage.setItem(STORAGE_KEYS.DUPLICATE_RULE_SEARCH, JSON.stringify(searchForm))
    } catch (error) {
      console.error('保存搜索表单失败:', error)
    }
  }

  /**
   * 清空所有数据
   */
  static clearAll(): void {
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key)
      })
    } catch (error) {
      console.error('清空数据失败:', error)
    }
  }
}

/**
 * 重复数据统计规则存储管理类
 */
export class DuplicateStatisticsStorage {
  private static readonly STORAGE_KEY = 'duplicate_statistics_rules'
  private static readonly PAGINATION_KEY = 'duplicate_statistics_pagination'

  // 获取重复数据统计规则列表
  static getStatisticsRuleList(): DuplicateStatisticsRule[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY)
      if (data) {
        return JSON.parse(data)
      }
      // 如果没有数据，返回默认模拟数据
      const defaultData = this.generateDefaultStatisticsData()
      this.saveStatisticsRuleList(defaultData)
      return defaultData
    } catch (error) {
      console.error('获取重复数据统计规则列表失败:', error)
      return []
    }
  }

  // 保存重复数据统计规则列表
  static saveStatisticsRuleList(rules: DuplicateStatisticsRule[]): boolean {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(rules))
      return true
    } catch (error) {
      console.error('保存重复数据统计规则列表失败:', error)
      return false
    }
  }

  // 添加重复数据统计规则
  static addStatisticsRule(rule: Omit<DuplicateStatisticsRule, 'id' | 'createTime'>): boolean {
    try {
      const rules = this.getStatisticsRuleList()
      const newRule: DuplicateStatisticsRule = {
        ...rule,
        id: `stats_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createTime: new Date().toLocaleString('zh-CN')
      }
      rules.unshift(newRule)
      return this.saveStatisticsRuleList(rules)
    } catch (error) {
      console.error('添加重复数据统计规则失败:', error)
      return false
    }
  }

  // 删除重复数据统计规则
  static deleteStatisticsRule(id: string): boolean {
    try {
      const rules = this.getStatisticsRuleList()
      const filteredRules = rules.filter(rule => rule.id !== id)
      return this.saveStatisticsRuleList(filteredRules)
    } catch (error) {
      console.error('删除重复数据统计规则失败:', error)
      return false
    }
  }

  // 根据ID获取重复数据统计规则
  static getStatisticsRuleById(id: string): DuplicateStatisticsRule | null {
    try {
      const rules = this.getStatisticsRuleList()
      return rules.find(rule => rule.id === id) || null
    } catch (error) {
      console.error('获取重复数据统计规则失败:', error)
      return null
    }
  }

  // 保存分页配置
  static savePaginationConfig(config: PaginationConfig): boolean {
    try {
      localStorage.setItem(this.PAGINATION_KEY, JSON.stringify(config))
      return true
    } catch (error) {
      console.error('保存分页配置失败:', error)
      return false
    }
  }

  // 获取分页配置
  static getPaginationConfig(): PaginationConfig {
    try {
      const data = localStorage.getItem(this.PAGINATION_KEY)
      if (data) {
        return JSON.parse(data)
      }
      return { page: 1, pageSize: 10, total: 0 }
    } catch (error) {
      console.error('获取分页配置失败:', error)
      return { page: 1, pageSize: 10, total: 0 }
    }
  }

  // 生成默认的重复数据统计模拟数据
  private static generateDefaultStatisticsData(): DuplicateStatisticsRule[] {
    const reportTypes: Array<'business' | 'temporary'> = ['business', 'temporary']
    const fields = ['年龄', '电话', '民族', '姓名', '身份证', '地址', '时间']
    const matchTypes: Array<'exact' | 'fuzzy' | 'regex'> = ['exact', 'fuzzy', 'regex']
    const outputIndicators = ['total_duplicate_count', 'duplicate_ratio', 'max_duplicate_count']
    const outputFormats = ['.xls', '.pdf']

    const rules: DuplicateStatisticsRule[] = []

    for (let i = 1; i <= 25; i++) {
      const reportAType = reportTypes[Math.floor(Math.random() * reportTypes.length)]
      const reportBType = reportTypes[Math.floor(Math.random() * reportTypes.length)]
      const matchType = matchTypes[Math.floor(Math.random() * matchTypes.length)]

      // 随机选择2-4个字段
      const reportAFields = this.getRandomFields(fields, 2, 4)
      const reportBFields = this.getRandomFields(fields, 2, 4)

      // 随机选择1-3个输出指标
      const selectedIndicators = this.getRandomFields(outputIndicators, 1, 3)

      // 随机选择1-2个输出格式
      const selectedFormats = this.getRandomFields(outputFormats, 1, 2)

      const baseTime = new Date('2024-04-01')
      const randomTime = new Date(baseTime.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000)

      rules.push({
        id: `stats_${Date.now()}_${i}`,
        createTime: randomTime.toLocaleString('zh-CN'),
        reportAType,
        reportAFields,
        reportBType,
        reportBFields,
        outputIndicators: selectedIndicators,
        matchType,
        outputFormats: selectedFormats
      })
    }

    return rules.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
  }

  // 随机选择字段的辅助方法
  private static getRandomFields(fields: string[], min: number, max: number): string[] {
    const count = Math.floor(Math.random() * (max - min + 1)) + min
    const shuffled = [...fields].sort(() => 0.5 - Math.random())
    return shuffled.slice(0, count)
  }
}
