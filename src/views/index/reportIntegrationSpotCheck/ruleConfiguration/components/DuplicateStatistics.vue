<script setup lang="ts" name="DuplicateStatistics">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type {
  DuplicateStatisticsRule,
  PaginationConfig,
  TableColumn,
  ActionButton
} from '../types'
import {
  REPORT_TYPE_OPTIONS,
  MATCH_TYPE_OPTIONS,
  FIELD_OPTIONS
} from '../types'
import { DuplicateStatisticsStorage } from '../storage'

// Props
interface Props {
  tableHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  tableHeight: 400
})

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// 响应式数据
const loading = ref(false)

// 表格相关
const statisticsTableRef = ref()
const statisticsTableData = ref<DuplicateStatisticsRule[]>([])
const statisticsFilteredData = ref<DuplicateStatisticsRule[]>([])
const selectedStatisticsRows = ref<DuplicateStatisticsRule[]>([])

// 分页配置
const statisticsPagination = reactive<PaginationConfig>({
  page: 1,
  size: 10,
  total: 0
})

// 重复数据统计弹窗相关
const showStatisticsDialogForm = ref(false)
const statisticsDialogFormRef = ref()
const statisticsDialogForm = ref({
  reportAType: 'business' as 'business' | 'temporary',
  reportAFields: [] as string[],
  reportBType: 'business' as 'business' | 'temporary',
  reportBFields: [] as string[],
  outputIndicators: [] as string[],
  matchType: 'exact' as 'exact' | 'fuzzy' | 'regex',
  outputFormats: [] as string[]
})

// 查看详情弹窗相关
const showViewDialog = ref(false)
const viewDialogData = ref<DuplicateStatisticsRule | null>(null)

// 表格列配置
const statisticsColumns: TableColumn[] = [
  { prop: 'createTime', label: '创建时间' },
  { prop: 'reportAType', label: '报表A类型' },
  { prop: 'reportAFields', label: '报表A参与统计字段' },
  { prop: 'reportBType', label: '报表B类型' },
  { prop: 'reportBFields', label: '报表B参与统计字段' },
  { prop: 'matchType', label: '匹配方式' }
]

// 操作按钮配置
const statisticsButtons: ActionButton[] = [
  { label: '查看', type: 'primary', code: 'view' },
  { label: '删除', type: 'danger', code: 'delete' }
]

// 输出指标选项
const OUTPUT_INDICATOR_OPTIONS = [
  { label: '总重复数量', value: 'total_duplicate_count' },
  { label: '重复占比', value: 'duplicate_ratio' },
  { label: '最大重复数', value: 'max_duplicate_count' }
]

// 输出格式选项
const OUTPUT_FORMAT_OPTIONS = [
  { label: '.xls', value: '.xls' },
  { label: '.pdf', value: '.pdf' }
]

// 表单验证规则
const statisticsDialogFormRules = {
  reportAType: [{ required: true, message: '请选择报表A类型', trigger: 'change' }],
  reportAFields: [{ required: true, message: '请选择报表A参与统计字段', trigger: 'change' }],
  reportBType: [{ required: true, message: '请选择报表B类型', trigger: 'change' }],
  reportBFields: [{ required: true, message: '请选择报表B参与统计字段', trigger: 'change' }],
  outputIndicators: [{ required: true, message: '请选择输出指标', trigger: 'change' }],
  matchType: [{ required: true, message: '请选择匹配方式', trigger: 'change' }],
  outputFormats: [{ required: true, message: '请选择输出格式', trigger: 'change' }]
}

// 当前页数据
const currentStatisticsPageData = computed(() => {
  const start = (statisticsPagination.page - 1) * statisticsPagination.size
  const end = start + statisticsPagination.size
  return statisticsFilteredData.value.slice(start, end)
})

// 初始化数据
const initStatisticsData = () => {
  console.log('初始化重复数据统计数据')
  loading.value = true

  // 模拟加载延迟
  setTimeout(() => {
    // 从本地存储获取数据
    statisticsTableData.value = DuplicateStatisticsStorage.getStatisticsRuleList()
    statisticsFilteredData.value = [...statisticsTableData.value]
    statisticsPagination.total = statisticsFilteredData.value.length

    console.log('统计数据加载完成:', {
      tableData: statisticsTableData.value.length,
      filteredData: statisticsFilteredData.value.length,
      total: statisticsPagination.total,
      pagination: statisticsPagination
    })

    // 恢复分页状态
    const savedPagination = DuplicateStatisticsStorage.getPaginationConfig()
    Object.assign(statisticsPagination, savedPagination)

    loading.value = false
  }, 500)
}

// 添加统计规则
const onAddStatisticsRule = () => {
  showStatisticsDialogForm.value = true
  // 重置表单
  statisticsDialogForm.value = {
    reportAType: 'business',
    reportAFields: [],
    reportBType: 'business',
    reportBFields: [],
    outputIndicators: [],
    matchType: 'exact',
    outputFormats: []
  }
}

// 提交统计规则表单
const onStatisticsDialogConfirm = async () => {
  if (!statisticsDialogFormRef.value) return

  try {
    await statisticsDialogFormRef.value.validate()

    loading.value = true

    setTimeout(() => {
      const success = DuplicateStatisticsStorage.addStatisticsRule(statisticsDialogForm.value)

      if (success) {
        ElMessage.success('添加规则成功')
        showStatisticsDialogForm.value = false
        initStatisticsData()
        emit('refresh')
      } else {
        ElMessage.error('添加规则失败')
      }

      loading.value = false
    }, 500)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消统计规则表单
const onStatisticsDialogCancel = () => {
  showStatisticsDialogForm.value = false
}

// 统计规则表格操作
const onStatisticsTableAction = ({ row, btn }: any) => {
  console.log('统计规则表格操作:', btn.code, row)
  switch (btn.code) {
    case 'view':
      onViewStatisticsRule(row)
      break
    case 'delete':
      onDeleteStatisticsRule(row)
      break
  }
}

// 查看统计规则详情
const onViewStatisticsRule = (row: DuplicateStatisticsRule) => {
  console.log('查看统计规则详情:', row)
  viewDialogData.value = row
  showViewDialog.value = true
}

// 删除统计规则
const onDeleteStatisticsRule = (row: DuplicateStatisticsRule) => {
  console.log('删除统计规则:', row)
  ElMessageBox.confirm(
    '确认删除这条规则吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    loading.value = true

    setTimeout(() => {
      const success = DuplicateStatisticsStorage.deleteStatisticsRule(row.id)

      if (success) {
        ElMessage.success('删除规则成功')
        initStatisticsData()
        emit('refresh')
      } else {
        ElMessage.error('删除规则失败')
      }

      loading.value = false
    }, 500)
  }).catch(() => {
    // 用户取消删除
  })
}

// 关闭查看详情弹窗
const onCloseViewDialog = () => {
  showViewDialog.value = false
  viewDialogData.value = null
}

// 统计规则分页变化
const onStatisticsPaginationChange = (page: number) => {
  statisticsPagination.page = page
  DuplicateStatisticsStorage.savePaginationConfig(statisticsPagination)
}

// 统计规则每页大小变化
const onStatisticsSizeChange = (size: number) => {
  statisticsPagination.size = size
  statisticsPagination.page = 1
  DuplicateStatisticsStorage.savePaginationConfig(statisticsPagination)
}

// 统计规则表格选择变化
const onStatisticsSelectionChange = (selection: DuplicateStatisticsRule[]) => {
  selectedStatisticsRows.value = selection
}

// 批量删除统计规则
const onBatchDeleteStatistics = () => {
  if (selectedStatisticsRows.value.length === 0) {
    ElMessage.warning('请选择要删除的规则')
    return
  }

  ElMessageBox.confirm(
    `确认删除选中的 ${selectedStatisticsRows.value.length} 条规则吗？`,
    '批量删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    loading.value = true

    setTimeout(() => {
      let successCount = 0
      selectedStatisticsRows.value.forEach(row => {
        if (DuplicateStatisticsStorage.deleteStatisticsRule(row.id)) {
          successCount++
        }
      })

      if (successCount > 0) {
        ElMessage.success(`成功删除 ${successCount} 条规则`)
        selectedStatisticsRows.value = []
        initStatisticsData()
        emit('refresh')
      } else {
        ElMessage.error('删除失败')
      }

      loading.value = false
    }, 500)
  }).catch(() => {
    // 用户取消删除
  })
}

// 暴露方法给父组件
defineExpose({
  initStatisticsData,
  onAddStatisticsRule,
  onBatchDeleteStatistics,
  selectedStatisticsRows: computed(() => selectedStatisticsRows.value)
})

// 组件挂载
onMounted(() => {
  initStatisticsData()
})
</script>

<template>
  <div class="duplicate-statistics">
    <!-- 重复数据统计表格 -->
    <TableV2
      ref="statisticsTableRef"
      :tableData="currentStatisticsPageData"
      :columns="statisticsColumns"
      :enable-toolbar="false"
      :enable-own-button="false"
      :enable-selection="true"
      :enable-index="true"
      :height="tableHeight"
      :buttons="statisticsButtons"
      :loading="loading"
      @click-button="onStatisticsTableAction"
      @selection-change="onStatisticsSelectionChange"
    >
      <!-- 报表A类型列自定义显示 -->
      <template #reportAType="{ row }">
        <el-tag :type="row.reportAType === 'business' ? 'primary' : 'success'">
          {{ row.reportAType === 'business' ? '业务表' : '临时表' }}
        </el-tag>
      </template>

      <!-- 报表A参与统计字段列自定义显示 -->
      <template #reportAFields="{ row }">
        <span>{{ row.reportAFields.join('、') }}</span>
      </template>

      <!-- 报表B类型列自定义显示 -->
      <template #reportBType="{ row }">
        <el-tag :type="row.reportBType === 'business' ? 'primary' : 'success'">
          {{ row.reportBType === 'business' ? '业务表' : '临时表' }}
        </el-tag>
      </template>

      <!-- 报表B参与统计字段列自定义显示 -->
      <template #reportBFields="{ row }">
        <span>{{ row.reportBFields.join('、') }}</span>
      </template>

      <!-- 匹配方式列自定义显示 -->
      <template #matchType="{ row }">
        <el-tag
          :type="row.matchType === 'exact' ? 'success' : row.matchType === 'fuzzy' ? 'warning' : 'info'"
        >
          {{ MATCH_TYPE_OPTIONS.find(opt => opt.value === row.matchType)?.label || row.matchType }}
        </el-tag>
      </template>
    </TableV2>

    <!-- 分页 -->
    <Pagination
      :total="statisticsPagination.total"
      :current-page="statisticsPagination.page"
      :page-size="statisticsPagination.size"
      @current-change="onStatisticsPaginationChange"
      @size-change="onStatisticsSizeChange"
    />
    <!-- 重复数据统计添加规则弹窗 -->
    <Dialog
      v-model="showStatisticsDialogForm"
      title="添加规则"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      width="900px"
      @closed="onStatisticsDialogCancel"
      @click-confirm="onStatisticsDialogConfirm"
    >
      <div class="dialog-content">
        <el-form
          ref="statisticsDialogFormRef"
          :model="statisticsDialogForm"
          :rules="statisticsDialogFormRules"
          label-width="140px"
          label-position="right"
        >
          <!-- 报表A配置 -->
          <div class="form-section">
            <h4>报表A</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="报表类型" prop="reportAType">
                  <el-select
                    v-model="statisticsDialogForm.reportAType"
                    placeholder="请选择报表类型"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="option in REPORT_TYPE_OPTIONS"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="参与统计字段" prop="reportAFields">
                  <el-select
                    v-model="statisticsDialogForm.reportAFields"
                    placeholder="请选择参与统计字段"
                    style="width: 100%"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                  >
                    <el-option
                      v-for="option in FIELD_OPTIONS"
                      :key="option.value"
                      :label="option.label"
                      :value="option.label"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 报表B配置 -->
          <div class="form-section">
            <h4>报表B</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="报表类型" prop="reportBType">
                  <el-select
                    v-model="statisticsDialogForm.reportBType"
                    placeholder="请选择报表类型"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="option in REPORT_TYPE_OPTIONS"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="参与统计字段" prop="reportBFields">
                  <el-select
                    v-model="statisticsDialogForm.reportBFields"
                    placeholder="请选择参与统计字段"
                    style="width: 100%"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                  >
                    <el-option
                      v-for="option in FIELD_OPTIONS"
                      :key="option.value"
                      :label="option.label"
                      :value="option.label"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 输出配置 -->
          <div class="form-section">
            <h4>输出配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="输出指标" prop="outputIndicators">
                  <el-checkbox-group v-model="statisticsDialogForm.outputIndicators">
                    <el-checkbox
                      v-for="option in OUTPUT_INDICATOR_OPTIONS"
                      :key="option.value"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="匹配方式" prop="matchType">
                  <el-radio-group v-model="statisticsDialogForm.matchType">
                    <el-radio
                      v-for="option in MATCH_TYPE_OPTIONS"
                      :key="option.value"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="输出格式" prop="outputFormats">
                  <el-checkbox-group v-model="statisticsDialogForm.outputFormats">
                    <el-checkbox
                      v-for="option in OUTPUT_FORMAT_OPTIONS"
                      :key="option.value"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </Dialog>

    <!-- 查看详情弹窗 -->
    <Dialog
      v-model="showViewDialog"
      title="查看详情"
      :destroy-on-close="true"
      width="900px"
      :show-confirm="false"
      @closed="onCloseViewDialog"
    >
      <div class="dialog-content" v-if="viewDialogData">
        <el-form
          :model="viewDialogData"
          label-width="140px"
          label-position="right"
          disabled
        >
          <!-- 报表A配置 -->
          <div class="form-section">
            <h4>报表A</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="报表类型">
                  <el-input :value="viewDialogData.reportAType === 'business' ? '业务表' : '临时表'" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="参与统计字段">
                  <el-input :value="viewDialogData.reportAFields.join('、')" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 报表B配置 -->
          <div class="form-section">
            <h4>报表B</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="报表类型">
                  <el-input :value="viewDialogData.reportBType === 'business' ? '业务表' : '临时表'" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="参与统计字段">
                  <el-input :value="viewDialogData.reportBFields.join('、')" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 输出配置 -->
          <div class="form-section">
            <h4>输出配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="输出指标">
                  <el-input :value="viewDialogData.outputIndicators.map(v => OUTPUT_INDICATOR_OPTIONS.find(opt => opt.value === v)?.label || v).join('、')" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="匹配方式">
                  <el-input :value="viewDialogData ? (MATCH_TYPE_OPTIONS.find(opt => opt.value === viewDialogData.matchType)?.label || viewDialogData.matchType) : ''" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="输出格式">
                  <el-input :value="viewDialogData.outputFormats.join('、')" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </Dialog>
  </div>
</template>

<style scoped lang="scss">
.duplicate-statistics {
  .dialog-content {
    padding: 20px 0;

    :deep(.el-form-item) {
      margin-bottom: 20px;
    }

    :deep(.el-textarea__inner) {
      resize: vertical;
    }

    .form-section {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 16px 0;
        padding: 8px 12px;
        background: #f5f7fa;
        border-left: 4px solid #409eff;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }
    }
  }
}
</style>
