<script setup lang="ts" name="DuplicateJudgment">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type {
  DuplicateRule,
  DuplicateRuleForm,
  PaginationConfig,
  TableColumn,
  ActionButton
} from '../types'
import {
  REPORT_TYPE_OPTIONS,
  MATCH_TYPE_OPTIONS,
  FIELD_OPTIONS
} from '../types'
import { DuplicateRuleStorage } from '../storage'

// Props
interface Props {
  tableHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  tableHeight: 400
})

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const currentRow = ref<DuplicateRule | null>(null)

// 表格相关
const tableRef = ref()
const tableData = ref<DuplicateRule[]>([])
const filteredData = ref<DuplicateRule[]>([])

// 分页配置
const pagination = reactive<PaginationConfig>({
  page: 1,
  size: 10,
  total: 0
})

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const dialogForm = ref<DuplicateRuleForm>({
  reportAType: 'business',
  reportAField: '',
  reportBType: 'business',
  reportBField: '',
  matchType: 'exact'
})

// 表格列配置
const columns: TableColumn[] = [
  { prop: 'createTime', label: '创建时间' },
  { prop: 'reportAType', label: '报表A类型' },
  { prop: 'reportAField', label: '报表A字段' },
  { prop: 'reportBType', label: '报表B类型' },
  { prop: 'reportBField', label: '报表B字段' },
  { prop: 'matchType', label: '匹配方式' }
]

// 操作按钮配置
const buttons: ActionButton[] = [
  { label: '编辑', type: 'primary', code: 'edit' },
  { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' }
]

// 表单验证规则
const dialogFormRules = {
  reportAType: [{ required: true, message: '请选择报表A类型', trigger: 'change' }],
  reportAField: [{ required: true, message: '请输入报表A字段', trigger: 'blur' }],
  reportBType: [{ required: true, message: '请选择报表B类型', trigger: 'change' }],
  reportBField: [{ required: true, message: '请输入报表B字段', trigger: 'blur' }],
  matchType: [{ required: true, message: '请选择匹配方式', trigger: 'change' }]
}

// 计算属性 - 分页后的数据
const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filteredData.value.slice(start, end)
})

// 初始化数据
const initData = () => {
  loading.value = true

  // 模拟加载延迟
  setTimeout(() => {
    // 从本地存储获取数据
    tableData.value = DuplicateRuleStorage.getRuleList()
    filteredData.value = [...tableData.value]
    pagination.total = filteredData.value.length

    // 恢复分页状态
    const savedPagination = DuplicateRuleStorage.getPaginationConfig()
    Object.assign(pagination, savedPagination)

    loading.value = false
  }, 500)
}

// 新增规则
const onClickAdd = () => {
  currentRow.value = null
  dialogForm.value = {
    reportAType: 'business',
    reportAField: '',
    reportBType: 'business',
    reportBField: '',
    matchType: 'exact'
  }
  showDialogForm.value = true
}

// 表格操作按钮点击
const onTableClickButton = ({ row, btn }: any) => {
  if (btn.code === 'edit') {
    currentRow.value = row
    dialogForm.value = {
      reportAType: row.reportAType,
      reportAField: row.reportAField,
      reportBType: row.reportBType,
      reportBField: row.reportBField,
      matchType: row.matchType
    }
    showDialogForm.value = true
  } else if (btn.code === 'delete') {
    const success = DuplicateRuleStorage.deleteRule(row.id)
    if (success) {
      ElMessage.success('删除成功')
      initData()
      emit('refresh')
    } else {
      ElMessage.error('删除失败')
    }
  }
}

// 分页变化
const onPaginationChange = (val: number, type: string) => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1
  }
  DuplicateRuleStorage.savePaginationConfig(pagination)
}

// 弹窗确认
const onDialogConfirm = () => {
  dialogFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true

      setTimeout(() => {
        if (currentRow.value) {
          // 编辑
          const success = DuplicateRuleStorage.updateRule(currentRow.value.id, {
            reportAType: dialogForm.value.reportAType,
            reportAField: dialogForm.value.reportAField,
            reportBType: dialogForm.value.reportBType,
            reportBField: dialogForm.value.reportBField,
            matchType: dialogForm.value.matchType
          })

          if (success) {
            ElMessage.success('编辑成功')
            showDialogForm.value = false
            initData()
            emit('refresh')
          } else {
            ElMessage.error('编辑失败')
          }
        } else {
          // 新增
          DuplicateRuleStorage.addRule({
            reportAType: dialogForm.value.reportAType,
            reportAField: dialogForm.value.reportAField,
            reportBType: dialogForm.value.reportBType,
            reportBField: dialogForm.value.reportBField,
            matchType: dialogForm.value.matchType
          })

          ElMessage.success('新增成功')
          showDialogForm.value = false
          initData()
          emit('refresh')
        }

        loading.value = false
      }, 500)
    }
  })
}

// 弹窗关闭
const onDialogClosed = () => {
  currentRow.value = null
  dialogForm.value = {
    reportAType: 'business',
    reportAField: '',
    reportBType: 'business',
    reportBField: '',
    matchType: 'exact'
  }
  // 清除表单验证状态
  if (dialogFormRef.value) {
    dialogFormRef.value.clearValidate()
  }
}

// 暴露方法给父组件
defineExpose({
  initData,
  onClickAdd
})

// 组件挂载
onMounted(() => {
  initData()
})
</script>

<template>
  <div class="duplicate-judgment">
    <!-- 重复判断表格 -->
    <TableV2
      ref="tableRef"
      :tableData="paginatedData"
      :columns="columns"
      :enable-toolbar="false"
      :enable-own-button="false"
      :enable-selection="false"
      :enable-index="true"
      :height="tableHeight"
      :buttons="buttons"
      :loading="loading"
      @click-button="onTableClickButton"
    >
      <!-- 报表A类型列自定义显示 -->
      <template #reportAType="{ row }">
        <el-tag :type="row.reportAType === 'business' ? 'primary' : 'success'">
          {{ row.reportAType === 'business' ? '业务表' : '临时表' }}
        </el-tag>
      </template>

      <!-- 报表B类型列自定义显示 -->
      <template #reportBType="{ row }">
        <el-tag :type="row.reportBType === 'business' ? 'primary' : 'success'">
          {{ row.reportBType === 'business' ? '业务表' : '临时表' }}
        </el-tag>
      </template>

      <!-- 匹配方式列自定义显示 -->
      <template #matchType="{ row }">
        <el-tag
          :type="row.matchType === 'exact' ? 'success' : row.matchType === 'fuzzy' ? 'warning' : 'info'"
        >
          {{ MATCH_TYPE_OPTIONS.find(opt => opt.value === row.matchType)?.label || row.matchType }}
        </el-tag>
      </template>
    </TableV2>

    <!-- 分页 -->
    <Pagination
      :total="pagination.total"
      :current-page="pagination.page"
      :page-size="pagination.size"
      @current-change="onPaginationChange($event, 'page')"
      @size-change="onPaginationChange($event, 'size')"
    />

    <!-- 添加/编辑规则弹窗 -->
    <Dialog
      v-model="showDialogForm"
      :title="currentRow ? '编辑规则' : '添加规则'"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      width="650px"
      @closed="onDialogClosed"
      @click-confirm="onDialogConfirm"
    >
      <div class="dialog-content">
        <el-form
          ref="dialogFormRef"
          :model="dialogForm"
          :rules="dialogFormRules"
          label-width="120px"
          label-position="right"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="报表A类型" prop="reportAType">
                <el-select
                  v-model="dialogForm.reportAType"
                  placeholder="请选择报表A类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in REPORT_TYPE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报表A字段" prop="reportAField">
                <el-select
                  v-model="dialogForm.reportAField"
                  placeholder="请选择报表A字段"
                  style="width: 100%"
                  filterable
                  allow-create
                >
                  <el-option
                    v-for="option in FIELD_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.label"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="报表B类型" prop="reportBType">
                <el-select
                  v-model="dialogForm.reportBType"
                  placeholder="请选择报表B类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in REPORT_TYPE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报表B字段" prop="reportBField">
                <el-select
                  v-model="dialogForm.reportBField"
                  placeholder="请选择报表B字段"
                  style="width: 100%"
                  filterable
                  allow-create
                >
                  <el-option
                    v-for="option in FIELD_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.label"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="匹配方式" prop="matchType">
                <el-select
                  v-model="dialogForm.matchType"
                  placeholder="请选择匹配方式"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in MATCH_TYPE_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </Dialog>
  </div>
</template>

<style scoped lang="scss">
.duplicate-judgment {
  .dialog-content {
    padding: 20px 0;

    :deep(.el-form-item) {
      margin-bottom: 20px;
    }

    :deep(.el-textarea__inner) {
      resize: vertical;
    }
  }
}
</style>
