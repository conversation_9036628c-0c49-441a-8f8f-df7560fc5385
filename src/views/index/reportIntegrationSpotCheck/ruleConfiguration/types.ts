// 规则配置相关类型定义

// 导航菜单项类型
export interface NavigationItem {
  id: string
  label: string
  icon?: string
  children?: NavigationItem[]
  path?: string
}

// 重复判断规则配置项类型
export interface DuplicateRule {
  id: string
  createTime: string
  reportAType: 'business' | 'temporary' // 报表A类型：业务表 | 临时表
  reportAField: string // 报表A字段
  reportBType: 'business' | 'temporary' // 报表B类型：业务表 | 临时表
  reportBField: string // 报表B字段
  matchType: 'exact' | 'fuzzy' | 'regex' // 匹配方式：完全匹配 | 模糊匹配 | 正则匹配
}

// 重复数据统计规则配置项类型
export interface DuplicateStatisticsRule {
  id: string
  createTime: string
  reportAType: 'business' | 'temporary' // 报表A类型：业务表 | 临时表
  reportAFields: string[] // 报表A参与统计字段（多选）
  reportBType: 'business' | 'temporary' // 报表B类型：业务表 | 临时表
  reportBFields: string[] // 报表B参与统计字段（多选）
  outputIndicators: string[] // 输出指标（多选）
  matchType: 'exact' | 'fuzzy' | 'regex' // 匹配方式：完全匹配 | 模糊匹配 | 正则匹配
  outputFormats: string[] // 输出格式（多选）
}

// 表格列配置
export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
}

// 分页配置
export interface PaginationConfig {
  page: number
  size: number
  total: number
}

// 重复判断查询表单
export interface DuplicateSearchForm {
  reportAType: string
  reportAField: string
  reportBType: string
  reportBField: string
  matchType: string
}

// 重复判断规则表单
export interface DuplicateRuleForm {
  reportAType: 'business' | 'temporary'
  reportAField: string
  reportBType: 'business' | 'temporary'
  reportBField: string
  matchType: 'exact' | 'fuzzy' | 'regex'
}

// 表单验证规则
export interface FormRules {
  [key: string]: Array<{
    required?: boolean
    message: string
    trigger: string
    min?: number
    max?: number
  }>
}

// 操作按钮配置
export interface ActionButton {
  label: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  code: string
  icon?: string
  popconfirm?: string
}

// 本地存储键名
export const STORAGE_KEYS = {
  DUPLICATE_RULE_LIST: 'duplicate_rule_list',
  DUPLICATE_RULE_PAGINATION: 'duplicate_rule_pagination',
  DUPLICATE_RULE_SEARCH: 'duplicate_rule_search'
} as const

// 报表类型选项
export const REPORT_TYPE_OPTIONS = [
  { label: '业务表', value: 'business' },
  { label: '临时表', value: 'temporary' }
] as const

// 匹配方式选项
export const MATCH_TYPE_OPTIONS = [
  { label: '完全匹配', value: 'exact' },
  { label: '模糊匹配', value: 'fuzzy' },
  { label: '正则匹配', value: 'regex' }
] as const

// 字段选项（模拟数据）
export const FIELD_OPTIONS = [
  { label: '业务表', value: 'business' },
  { label: '临时表', value: 'temporary' },
  { label: '电话', value: 'phone' },
  { label: '手机', value: 'mobile' },
  { label: '身份证', value: 'idcard' },
  { label: '姓名', value: 'name' },
  { label: '地址', value: 'address' },
  { label: '邮箱', value: 'email' }
] as const
