import type { DuplicateRule, NavigationItem } from './types'

// 生成模拟重复判断规则数据
export const generateMockDuplicateRuleData = (): DuplicateRule[] => {
  const mockData: DuplicateRule[] = []
  const reportTypes: ('business' | 'temporary')[] = ['business', 'temporary']
  const matchTypes: ('exact' | 'fuzzy' | 'regex')[] = ['exact', 'fuzzy', 'regex']
  const fields = ['业务表', '临时表', '电话', '手机', '身份证', '姓名', '地址', '邮箱']

  for (let i = 1; i <= 25; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

    mockData.push({
      id: `duplicate_rule_${i.toString().padStart(3, '0')}`,
      createTime: createTime.toISOString().replace('T', ' ').split('.')[0],
      reportAType: reportTypes[Math.floor(Math.random() * reportTypes.length)],
      reportAField: fields[Math.floor(Math.random() * fields.length)],
      reportBType: reportTypes[Math.floor(Math.random() * reportTypes.length)],
      reportBField: fields[Math.floor(Math.random() * fields.length)],
      matchType: matchTypes[Math.floor(Math.random() * matchTypes.length)]
    })
  }

  return mockData
}

// 左侧导航菜单数据
export const navigationMenuData: NavigationItem[] = [
  {
    id: 'duplicate_judgment',
    label: '重复判断',
    icon: 'Document',
    path: '/duplicate-judgment'
  },
  {
    id: 'duplicate_statistics',
    label: '重复数据统计',
    icon: 'DataAnalysis',
    path: '/duplicate-statistics'
  },
  {
    id: 'exclude_rules',
    label: '排除规则',
    icon: 'Filter',
    children: [
      {
        id: 'business_table_identification',
        label: '标识业务表',
        path: '/business-table-identification'
      },
      {
        id: 'temp_to_business',
        label: '临时表转业务表',
        path: '/temp-to-business'
      }
    ]
  },
  {
    id: 'compare_config',
    label: '比对配置',
    icon: 'Compare',
    path: '/compare-config'
  },
  {
    id: 'precise_calc_config',
    label: '精准计算配置',
    icon: 'Calculator',
    path: '/precise-calc-config'
  },
  {
    id: 'data_consistency_check',
    label: '数据一致性、完整性、兼容性检查',
    icon: 'Check',
    path: '/data-consistency-check'
  },
  {
    id: 'exception_data_handle',
    label: '异常数据处理',
    icon: 'Warning',
    path: '/exception-data-handle'
  },
  {
    id: 'data_validation_setting',
    label: '数据校验设置',
    icon: 'Setting',
    path: '/data-validation-setting'
  },
  {
    id: 'duplicate_data_setting',
    label: '重复数据设置',
    icon: 'CopyDocument',
    path: '/duplicate-data-setting'
  },
  {
    id: 'data_visualization_setting',
    label: '数据可视化设置',
    icon: 'TrendCharts',
    path: '/data-visualization-setting'
  }
]

// 默认的模拟数据
export const defaultMockData = generateMockDuplicateRuleData()
