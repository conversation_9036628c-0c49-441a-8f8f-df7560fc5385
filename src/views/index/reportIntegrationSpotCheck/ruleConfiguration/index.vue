<script setup lang="ts" name="ruleConfiguration">
import { ref, computed, onMounted, nextTick } from 'vue'
import { navigationMenuData } from './mockData'
import DuplicateJudgment from './components/DuplicateJudgment.vue'
import DuplicateStatistics from './components/DuplicateStatistics.vue'

// 响应式数据
const tableHeight = ref(0)
const selectedNavItem = ref('duplicate_judgment')

// 组件引用
const duplicateJudgmentRef = ref()
const duplicateStatisticsRef = ref()

// 判断当前显示的内容类型
const isStatisticsView = computed(() => selectedNavItem.value === 'duplicate_statistics')



// 新增规则
const onClickAdd = () => {
  if (isStatisticsView.value) {
    duplicateStatisticsRef.value?.onAddStatisticsRule()
  } else {
    duplicateJudgmentRef.value?.onClickAdd()
  }
}

// 批量删除统计规则
const onBatchDeleteStatistics = () => {
  duplicateStatisticsRef.value?.onBatchDeleteStatistics()
}

// 左侧导航点击
const onNavItemClick = async (menuIndex: string) => {
  selectedNavItem.value = menuIndex

  // 等待DOM更新完成
  await nextTick()
  initCurrentComponentData()
}

// 块高度变化
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 120
}

// 获取当前页面标题
const getCurrentPageTitle = () => {
  const titleMap: Record<string, string> = {
    'duplicate_judgment': '重复判断',
    'duplicate_statistics': '重复数据统计',
    'exclude_rules': '排除规则',
    'business_table_identification': '标识业务表',
    'temp_to_business': '临时表转业务表',
    'compare_config': '比对配置',
    'precise_calc_config': '精准计算配置',
    'data_consistency_check': '数据一致性、完整性、兼容性检查',
    'exception_data_handle': '异常数据处理',
    'data_validation_setting': '数据校验设置',
    'duplicate_data_setting': '重复数据设置',
    'data_visualization_setting': '数据可视化设置'
  }

  return titleMap[selectedNavItem.value] || '规则配置'
}

// 初始化当前组件数据
const initCurrentComponentData = async () => {
  // 等待DOM更新完成
  await nextTick()

  // 根据当前选中的导航项初始化对应的数据
  if (selectedNavItem.value === 'duplicate_statistics') {
    duplicateStatisticsRef.value?.initStatisticsData()
  } else {
    duplicateJudgmentRef.value?.initData()
  }
}

// 组件挂载
onMounted(async () => {
  // 等待DOM更新完成
  await nextTick()
  initCurrentComponentData()
})
</script>

<template>
  <div class="rule-configuration">
    <div class="layout-container">
      <!-- 左侧导航菜单 -->
      <div class="sidebar">
        <div class="sidebar-title">规则配置</div>
        <el-menu
          :default-active="selectedNavItem"
          class="sidebar-menu"
          @select="onNavItemClick"
        >
          <template v-for="item in navigationMenuData" :key="item.id">
            <el-sub-menu v-if="item.children" :index="item.id">
              <template #title>
                <el-icon v-if="item.icon">
                  <component :is="item.icon" />
                </el-icon>
                <span>{{ item.label }}</span>
              </template>
              <el-menu-item
                v-for="child in item.children"
                :key="child.id"
                :index="child.id"
              >
                {{ child.label }}
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item v-else :index="item.id">
              <el-icon v-if="item.icon">
                <component :is="item.icon" />
              </el-icon>
              <span>{{ item.label }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </div>

      <!-- 右侧主内容区域 -->
      <div class="main-content">
        <Block
          :title="getCurrentPageTitle()"
          :enable-fixed-height="true"
          @height-changed="onBlockHeightChanged"
        >
          <template #topRight>
            <el-button
              v-if="!isStatisticsView"
              size="small"
              type="primary"
              @click="onClickAdd"
            >
              添加规则
            </el-button>
            <template v-if="isStatisticsView">
              <el-button
                size="small"
                type="primary"
                @click="onClickAdd"
              >
                添加规则
              </el-button>
              <el-button
                size="small"
                type="danger"
                :disabled="(duplicateStatisticsRef?.selectedStatisticsRows?.value?.value?.length || 0) === 0"
                @click="onBatchDeleteStatistics"
              >
                批量删除
              </el-button>
            </template>
          </template>

          <!-- 重复判断组件 -->
          <DuplicateJudgment
            v-if="!isStatisticsView"
            ref="duplicateJudgmentRef"
            :table-height="tableHeight"
          />

          <!-- 重复数据统计组件 -->
          <DuplicateStatistics
            v-if="isStatisticsView"
            ref="duplicateStatisticsRef"
            :table-height="tableHeight"
          />
        </Block>
      </div>
    </div>
  </div>
</template>

<route>
{
  meta: {
    title: '规则配置',
  },
}
</route>

<style scoped lang="scss">
.rule-configuration {
  height: 100%;

  .layout-container {
    display: flex;
    height: 100%;
    gap: 16px;
  }

  .sidebar {
    width: 280px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    border: 1px solid #e4e7ed;

    .sidebar-title {
      padding: 16px;
      font-size: 16px;
      font-weight: 600;
      background: #409eff;
      color: #ffffff;
      border-bottom: 1px solid #e9ecef;
      text-align: center;
    }

    .sidebar-menu {
      border: none;
      background: #fff;

      :deep(.el-menu-item) {
        height: 48px;
        line-height: 48px;
        padding-left: 20px !important;
        color: #303133;
        border-bottom: 1px solid #f5f7fa;
        font-size: 14px;
        font-weight: 500;

        &:hover {
          background-color: #ecf5ff;
          color: #409eff;
        }

        &.is-active {
          background-color: #409eff;
          color: #ffffff;
          font-weight: 600;
        }

        i,span{
          color: #333 !important;
        }
      }

      :deep(.el-sub-menu__title) {
        height: 48px;
        line-height: 48px;
        padding-left: 20px !important;
        color: #303133;
        font-weight: 600;
        font-size: 14px;
        border-bottom: 1px solid #f5f7fa;

        &:hover {
          background-color: #ecf5ff;
          color: #409eff;
        }

        i,span {
          color: #333 !important;
        }
      }

      :deep(.el-sub-menu .el-menu-item) {
        padding-left: 40px !important;
        height: 40px;
        line-height: 40px;
        color: #333 !important;
        font-size: 13px;
        font-weight: 500;

        &:hover {
          background-color: #f0f9ff;
          color: #409eff;
        }

        &.is-active {
          background-color: #409eff;
          color: #ffffff;
          font-weight: 600;
        }

      }

      :deep(.el-sub-menu.is-opened .el-sub-menu__title) {
        background-color: #f5f7fa;
        color: #409eff;
      }
    }
  }

  .main-content {
    flex: 1;
    min-width: 0;
  }
}
</style>