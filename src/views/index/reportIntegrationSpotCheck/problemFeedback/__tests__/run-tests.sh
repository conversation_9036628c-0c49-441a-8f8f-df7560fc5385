#!/bin/bash

# 问题反馈功能端到端测试运行脚本
# 用于执行完整的功能测试并生成报告

echo "🚀 开始执行问题反馈功能端到端测试..."

# 检查是否安装了Playwright
if ! command -v npx &> /dev/null; then
    echo "❌ 错误: 未找到npx命令，请确保已安装Node.js"
    exit 1
fi

# 创建测试结果目录
mkdir -p test-results

# 设置测试环境变量
export NODE_ENV=test
export CI=false

echo "📦 安装Playwright浏览器..."
npx playwright install

echo "🧪 运行测试套件..."

# 运行所有测试
npx playwright test --config=playwright.config.ts

# 检查测试结果
if [ $? -eq 0 ]; then
    echo "✅ 所有测试通过！"
    
    # 生成测试报告
    echo "📊 生成测试报告..."
    npx playwright show-report test-results/html-report
    
    echo "📋 测试总结:"
    echo "- HTML报告: test-results/html-report/index.html"
    echo "- JSON结果: test-results/test-results.json"
    echo "- JUnit报告: test-results/junit.xml"
    
else
    echo "❌ 测试失败，请查看详细报告"
    echo "📊 查看失败报告..."
    npx playwright show-report test-results/html-report
    exit 1
fi

echo "🎉 测试执行完成！"
