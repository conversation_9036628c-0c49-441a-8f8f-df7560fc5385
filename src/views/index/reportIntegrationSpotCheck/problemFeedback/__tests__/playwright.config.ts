import { defineConfig, devices } from '@playwright/test'

/**
 * 问题反馈功能的Playwright测试配置
 * 专门用于测试问题反馈主页面的端到端功能
 */
export default defineConfig({
  testDir: './',
  /* 并行运行测试 */
  fullyParallel: true,
  /* 在CI环境中失败时不重试 */
  forbidOnly: !!process.env.CI,
  /* 在CI环境中重试失败的测试 */
  retries: process.env.CI ? 2 : 0,
  /* 并行工作进程数量 */
  workers: process.env.CI ? 1 : undefined,
  /* 测试报告配置 */
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/test-results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }]
  ],
  /* 全局测试配置 */
  use: {
    /* 基础URL */
    baseURL: 'http://localhost:3000',
    
    /* 在失败时收集跟踪信息 */
    trace: 'on-first-retry',
    
    /* 截图配置 */
    screenshot: 'only-on-failure',
    
    /* 视频录制 */
    video: 'retain-on-failure',
    
    /* 浏览器上下文配置 */
    viewport: { width: 1280, height: 720 },
    
    /* 忽略HTTPS错误 */
    ignoreHTTPSErrors: true,
    
    /* 等待超时 */
    actionTimeout: 10000,
    navigationTimeout: 30000,
  },

  /* 测试项目配置 - 不同浏览器和设备 */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },

    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },

    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },

    /* 移动端测试 */
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },

    /* 平板测试 */
    {
      name: 'Tablet',
      use: { ...devices['iPad Pro'] },
    },
  ],

  /* 本地开发服务器配置 */
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },
})
