import { test, expect } from '@playwright/test'

test.describe('问题反馈主页面功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 清除本地存储，确保测试环境干净
    await page.evaluate(() => {
      localStorage.clear()
    })
    
    // 导航到问题反馈页面
    await page.goto('/reportIntegrationSpotCheck/problemFeedback')
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle')
  })

  test('页面基本元素渲染测试', async ({ page }) => {
    // 验证页面标题
    await expect(page.locator('text=问题反馈')).toBeVisible()
    
    // 验证顶部按钮
    await expect(page.locator('text=创建问题反馈表单')).toBeVisible()
    await expect(page.locator('text=问题反馈统计分析')).toBeVisible()
    
    // 验证搜索表单
    await expect(page.locator('input[placeholder*="问题标题"]')).toBeVisible()
    await expect(page.locator('text=查询')).toBeVisible()
    await expect(page.locator('text=重置')).toBeVisible()
    
    // 验证表格存在
    await expect(page.locator('table')).toBeVisible()
    
    // 验证分页组件
    await expect(page.locator('.el-pagination')).toBeVisible()
  })

  test('模拟数据加载测试', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('table tbody tr', { timeout: 5000 })
    
    // 验证表格有数据行
    const rows = await page.locator('table tbody tr').count()
    expect(rows).toBeGreaterThan(0)
    
    // 验证表格列标题
    await expect(page.locator('text=序号')).toBeVisible()
    await expect(page.locator('text=问题标题')).toBeVisible()
    await expect(page.locator('text=提交人')).toBeVisible()
    await expect(page.locator('text=提交时间')).toBeVisible()
    await expect(page.locator('text=状态')).toBeVisible()
    await expect(page.locator('text=优先级')).toBeVisible()
    await expect(page.locator('text=分类')).toBeVisible()
    await expect(page.locator('text=操作')).toBeVisible()
  })

  test('搜索功能测试', async ({ page }) => {
    // 等待页面加载完成
    await page.waitForSelector('table tbody tr')
    
    // 获取初始行数
    const initialRows = await page.locator('table tbody tr').count()
    
    // 输入搜索关键词
    await page.fill('input[placeholder*="问题标题"]', '系统')
    
    // 点击查询按钮
    await page.click('text=查询')
    
    // 等待加载状态完成
    await page.waitForTimeout(1000)
    
    // 验证搜索结果
    const filteredRows = await page.locator('table tbody tr').count()
    
    // 搜索后的结果应该不同（可能更少或相同）
    expect(filteredRows).toBeLessThanOrEqual(initialRows)
  })

  test('重置搜索功能测试', async ({ page }) => {
    // 等待页面加载完成
    await page.waitForSelector('table tbody tr')
    
    // 输入搜索条件
    await page.fill('input[placeholder*="问题标题"]', '测试')
    await page.selectOption('select', 'pending')
    
    // 点击查询
    await page.click('text=查询')
    await page.waitForTimeout(500)
    
    // 点击重置
    await page.click('text=重置')
    
    // 验证搜索框已清空
    const titleInput = await page.locator('input[placeholder*="问题标题"]').inputValue()
    expect(titleInput).toBe('')
  })

  test('分页功能测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('table tbody tr')
    
    // 验证分页组件存在
    await expect(page.locator('.el-pagination')).toBeVisible()
    
    // 如果有多页，测试翻页功能
    const nextButton = page.locator('.el-pagination .btn-next')
    if (await nextButton.isEnabled()) {
      // 记录第一页的第一行数据
      const firstRowText = await page.locator('table tbody tr:first-child').textContent()
      
      // 点击下一页
      await nextButton.click()
      await page.waitForTimeout(500)
      
      // 验证数据已更新
      const newFirstRowText = await page.locator('table tbody tr:first-child').textContent()
      expect(newFirstRowText).not.toBe(firstRowText)
    }
  })

  test('操作按钮功能测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('table tbody tr')
    
    // 测试查看按钮
    await page.click('table tbody tr:first-child button:has-text("查看")')
    
    // 验证消息提示
    await expect(page.locator('.el-message')).toBeVisible()
    
    // 等待消息消失
    await page.waitForTimeout(1000)
    
    // 测试导出按钮
    await page.click('table tbody tr:first-child button:has-text("导出")')
    await expect(page.locator('.el-message')).toBeVisible()
    
    await page.waitForTimeout(1000)
    
    // 测试打印按钮
    await page.click('table tbody tr:first-child button:has-text("打印")')
    await expect(page.locator('.el-message')).toBeVisible()
    
    await page.waitForTimeout(1000)
    
    // 测试分享按钮
    await page.click('table tbody tr:first-child button:has-text("分享")')
    await expect(page.locator('.el-message')).toBeVisible()
  })

  test('状态相关按钮显示逻辑测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('table tbody tr')
    
    // 获取所有行
    const rows = await page.locator('table tbody tr').count()
    
    for (let i = 0; i < Math.min(rows, 5); i++) {
      const row = page.locator(`table tbody tr:nth-child(${i + 1})`)
      const statusTag = row.locator('.el-tag')
      
      if (await statusTag.isVisible()) {
        const statusText = await statusTag.textContent()
        const viewReplyButton = row.locator('button:has-text("查看回复")')
        
        if (statusText === '已回复' || statusText === '已解决') {
          // 这些状态应该显示查看回复按钮
          await expect(viewReplyButton).toBeVisible()
        } else if (statusText === '未回复' || statusText === '已超时') {
          // 这些状态不应该显示查看回复按钮
          await expect(viewReplyButton).not.toBeVisible()
        }
      }
    }
  })

  test('顶部功能按钮测试', async ({ page }) => {
    // 测试创建问题反馈表单按钮
    await page.click('text=创建问题反馈表单')
    await expect(page.locator('.el-message')).toBeVisible()
    
    await page.waitForTimeout(1000)
    
    // 测试问题反馈统计分析按钮
    await page.click('text=问题反馈统计分析')
    await expect(page.locator('.el-message')).toBeVisible()
  })

  test('数据持久化测试', async ({ page }) => {
    // 等待初始数据加载
    await page.waitForSelector('table tbody tr')
    
    // 记录初始数据
    const initialRowCount = await page.locator('table tbody tr').count()
    const firstRowText = await page.locator('table tbody tr:first-child').textContent()
    
    // 刷新页面
    await page.reload()
    await page.waitForLoadState('networkidle')
    await page.waitForSelector('table tbody tr')
    
    // 验证数据持久化
    const afterReloadRowCount = await page.locator('table tbody tr').count()
    const afterReloadFirstRowText = await page.locator('table tbody tr:first-child').textContent()
    
    expect(afterReloadRowCount).toBe(initialRowCount)
    expect(afterReloadFirstRowText).toBe(firstRowText)
  })

  test('响应式设计测试', async ({ page }) => {
    // 测试桌面视图
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.waitForSelector('table tbody tr')
    
    // 验证表格在桌面视图下正常显示
    await expect(page.locator('table')).toBeVisible()
    
    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500)
    
    // 验证表格在平板视图下仍然可见
    await expect(page.locator('table')).toBeVisible()
    
    // 测试手机视图
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)
    
    // 验证表格在手机视图下仍然可见
    await expect(page.locator('table')).toBeVisible()
  })

  test('加载状态测试', async ({ page }) => {
    // 等待页面加载
    await page.waitForSelector('table tbody tr')

    // 输入搜索条件
    await page.fill('input[placeholder*="问题标题"]', '测试')

    // 点击查询并立即检查加载状态
    await page.click('text=查询')

    // 验证加载状态（可能很快消失，所以使用较短的超时）
    try {
      await expect(page.locator('.el-loading-mask')).toBeVisible({ timeout: 1000 })
    } catch (e) {
      // 加载状态可能已经消失，这是正常的
      console.log('Loading state finished quickly')
    }

    // 等待加载完成
    await page.waitForTimeout(1000)

    // 验证加载状态已消失
    await expect(page.locator('.el-loading-mask')).not.toBeVisible()
  })

})
