# 问题反馈主页面功能

这是一个完整的前端演示功能，实现了问题反馈系统的主要功能，包括问题列表展示、搜索、分页、操作按钮等。

## 功能特性

### 🎯 核心功能
- **问题反馈列表展示**：支持表格形式展示问题反馈信息
- **搜索过滤**：支持按问题标题和状态进行搜索
- **分页功能**：支持数据分页展示，提升性能
- **状态管理**：支持多种问题状态（未回复、已回复、已超时、已解决）
- **操作按钮**：根据状态动态显示不同的操作按钮

### 📊 数据管理
- **本地存储**：使用localStorage实现数据持久化
- **模拟数据**：内置28条模拟数据用于演示
- **数据一致性**：页面刷新后数据保持不丢失

### 🎨 用户界面
- **响应式设计**：支持桌面、平板、手机等不同屏幕尺寸
- **美观布局**：使用Element Plus组件库，界面美观统一
- **加载状态**：搜索时显示加载状态，提升用户体验

## 页面结构

```
问题反馈主页面
├── 顶部按钮区域
│   ├── 创建问题反馈表单
│   └── 问题反馈统计分析
├── 搜索区域
│   ├── 问题标题搜索
│   ├── 状态筛选
│   ├── 查询按钮
│   └── 重置按钮
├── 数据表格
│   ├── 序号
│   ├── 问题标题
│   ├── 提交人
│   ├── 提交时间
│   ├── 状态（标签显示）
│   ├── 优先级（标签显示）
│   ├── 分类
│   └── 操作按钮
│       ├── 生成报告栏（查看、导出、打印、分享）
│       └── 状态相关按钮（查看回复 - 仅已回复/已解决状态显示）
└── 分页组件
```

## 技术实现

### 🛠 技术栈
- **Vue 3** + **TypeScript**：现代化前端框架
- **Element Plus**：UI组件库
- **Composition API**：Vue 3组合式API
- **Reactive**：响应式数据管理
- **LocalStorage**：本地数据持久化

### 📁 文件结构
```
problemFeedback/
├── index.vue                 # 主页面组件
├── README.md                 # 说明文档
└── __tests__/               # 测试文件
    ├── problem-feedback.test.ts    # Playwright测试
    ├── playwright.config.ts        # 测试配置
    └── run-tests.sh                # 测试运行脚本
```

## 使用说明

### 🚀 快速开始

1. **访问页面**
   ```
   导航到: /reportIntegrationSpotCheck/problemFeedback
   ```

2. **查看问题列表**
   - 页面加载后自动显示问题反馈列表
   - 支持28条模拟数据展示

3. **搜索功能**
   - 在"问题标题"输入框中输入关键词
   - 选择状态进行筛选
   - 点击"查询"按钮执行搜索
   - 点击"重置"按钮清空搜索条件

4. **操作功能**
   - **查看**：查看问题详情
   - **导出**：导出问题数据
   - **打印**：打印问题信息
   - **分享**：分享问题链接
   - **查看回复**：查看问题回复（仅已回复/已解决状态显示）

### 📱 响应式支持

- **桌面端**（≥1200px）：完整功能展示
- **平板端**（768px-1199px）：优化布局
- **手机端**（<768px）：紧凑布局

## 测试说明

### 🧪 自动化测试

项目包含完整的Playwright端到端测试，覆盖以下场景：

1. **基础功能测试**
   - 页面元素渲染
   - 数据加载
   - 搜索功能
   - 分页功能

2. **交互功能测试**
   - 操作按钮点击
   - 状态相关按钮显示逻辑
   - 顶部功能按钮

3. **数据持久化测试**
   - 本地存储功能
   - 页面刷新数据保持

4. **响应式设计测试**
   - 不同屏幕尺寸适配
   - 移动端兼容性

### 🏃‍♂️ 运行测试

```bash
# 进入测试目录
cd src/views/index/reportIntegrationSpotCheck/problemFeedback/__tests__

# 运行测试脚本
./run-tests.sh

# 或者直接运行Playwright测试
npx playwright test --config=playwright.config.ts
```

### 📊 测试报告

测试完成后会生成以下报告：
- **HTML报告**：`test-results/html-report/index.html`
- **JSON结果**：`test-results/test-results.json`
- **JUnit报告**：`test-results/junit.xml`

## 数据结构

### 📋 问题反馈数据接口

```typescript
interface FeedbackItem {
  id: string                    // 唯一标识
  index: number                 // 序号
  title: string                 // 问题标题
  content: string               // 问题内容
  submitter: string             // 提交人
  submitTime: string            // 提交时间
  status: 'pending' | 'replied' | 'timeout' | 'resolved'  // 状态
  priority: 'low' | 'medium' | 'high'                     // 优先级
  category: string              // 分类
  replyContent?: string         // 回复内容（可选）
  replyTime?: string           // 回复时间（可选）
}
```

### 🗂 状态说明

- **pending**：未回复
- **replied**：已回复
- **timeout**：已超时
- **resolved**：已解决

### 🎯 优先级说明

- **low**：低优先级
- **medium**：中优先级
- **high**：高优先级

## 开发规范

### 📝 代码规范

- 遵循Vue 3 + TypeScript最佳实践
- 使用Composition API编写组件
- 采用响应式数据管理
- 遵循Element Plus组件使用规范

### 🔧 扩展指南

1. **添加新功能**
   - 在组件中添加新的方法和数据
   - 更新模板中的UI元素
   - 添加相应的测试用例

2. **修改数据结构**
   - 更新`FeedbackItem`接口定义
   - 修改模拟数据生成函数
   - 更新本地存储逻辑

3. **自定义样式**
   - 修改`<style>`部分的SCSS代码
   - 保持响应式设计原则
   - 确保与项目整体风格一致

## 注意事项

⚠️ **重要提醒**

1. **纯前端演示**：这是纯前端功能，无需后端API支持
2. **数据持久化**：数据存储在浏览器localStorage中
3. **测试环境**：测试前请确保开发服务器正常运行
4. **浏览器兼容**：建议使用现代浏览器进行测试

## 更新日志

### v1.0.0 (2024-07-24)
- ✨ 初始版本发布
- 🎯 实现完整的问题反馈主页面功能
- 📱 支持响应式设计
- 🧪 包含完整的自动化测试
- 📚 提供详细的文档说明
