<script setup lang="ts" name="problemFeedback">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Document, Picture, Delete, Upload, Clock, InfoFilled, Download, Printer, Share, ArrowLeft } from '@element-plus/icons-vue'

// 路由实例
const router = useRouter()

// 搜索表单
const searchFormProp = ref([
  { label: '问题标题', prop: 'title', type: 'text' },
  { label: '状态', prop: 'status', type: 'select', options: [
    { label: '全部', value: '' },
    { label: '未回复', value: 'pending' },
    { label: '已回复', value: 'replied' },
    { label: '已超时', value: 'timeout' },
    { label: '已解决', value: 'resolved' }
  ]}
])
const searchForm = ref({ title: '', status: '' })

// 加载状态
const loading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0)

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
})

// 问题反馈数据接口定义
interface FeedbackItem {
  id: string
  index: number
  title: string
  content: string
  submitter: string
  submitTime: string
  status: 'pending' | 'replied' | 'timeout' | 'resolved'
  priority: 'low' | 'medium' | 'high'
  category: string
  estimatedTime: string
  replyContent?: string
  replyTime?: string
}

// 本地存储key
const STORAGE_KEY = 'problem_feedback_data'

// 原始数据
const allFeedbackData = ref<FeedbackItem[]>([])

// 分页后的数据
const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filteredData.value.slice(start, end).map((item, index) => ({
    ...item,
    index: start + index + 1
  }))
})

// 过滤后的数据
const filteredData = computed(() => {
  let filtered = allFeedbackData.value
  
  if (searchForm.value.title) {
    filtered = filtered.filter(item => 
      item.title.toLowerCase().includes(searchForm.value.title.toLowerCase())
    )
  }
  
  if (searchForm.value.status) {
    filtered = filtered.filter(item => item.status === searchForm.value.status)
  }
  
  pagination.total = filtered.length
  return filtered
})

// 表格列定义 - 使用百分比宽度实现响应式设计
const columns = [
  { prop: 'title', label: '问题反馈标题' },
  { prop: 'submitTime', label: '创建时间' },
  { prop: 'category', label: '类别' },
  { prop: 'content', label: '问题详情' },
  { prop: 'priority', label: '优先级' },
  { prop: 'estimatedTime', label: '预计剩余时间' },
  { prop: 'status', label: '状态' },
  { prop: 'report', label: '生成报告',width: '220px' },
  { prop: 'action', label: '操作' }
]



// 生成模拟数据
const generateMockData = (): FeedbackItem[] => {
  const titles = [
    '系统登录异常问题', '数据导出功能故障', '页面加载缓慢', '权限设置错误',
    '报表生成失败', '文件上传问题', '搜索功能异常', '数据同步延迟',
    '界面显示错乱', '打印功能故障', '邮件通知失效', '数据备份问题',
    '用户权限管理', '系统性能优化', '安全漏洞修复', '功能需求建议',
    '操作流程改进', '界面优化建议', '数据统计错误', '系统集成问题',
    '移动端适配', '浏览器兼容性', '数据库连接异常', 'API接口错误',
    '缓存机制问题', '日志记录异常', '监控告警设置', '自动化部署'
  ]
  
  const submitters = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const categories = ['系统问题', '功能建议', '性能优化', '安全问题', '用户体验', '数据问题']
  const statuses: Array<'pending' | 'replied' | 'timeout' | 'resolved'> = ['pending', 'replied', 'timeout', 'resolved']
  const priorities: Array<'low' | 'medium' | 'high'> = ['low', 'medium', 'high']
  
  // 生成预计剩余时间
  const generateEstimatedTime = (status: string) => {
    switch (status) {
      case 'resolved':
        return '已完成'
      case 'timeout':
        return '已超时'
      default:
        const hours = Math.floor(Math.random() * 48) + 1
        return `${hours}小时`
    }
  }

  return Array.from({ length: 28 }, (_, index) => {
    const status = statuses[index % statuses.length]
    return {
      id: `feedback_${index + 1}`,
      index: index + 1,
      title: titles[index % titles.length],
      content: `这是关于${titles[index % titles.length]}的详细描述...`,
      submitter: submitters[index % submitters.length],
      submitTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString(),
      status: status,
      priority: priorities[index % priorities.length],
      category: categories[index % categories.length],
      estimatedTime: generateEstimatedTime(status),
      replyContent: index % 4 !== 0 ? '问题已处理，请查看相关解决方案。' : undefined,
      replyTime: index % 4 !== 0 ? new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000).toLocaleString() : undefined
    }
  })
}

// 从本地存储加载数据
const loadDataFromStorage = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    if (stored) {
      allFeedbackData.value = JSON.parse(stored)
    } else {
      // 如果没有存储数据，生成模拟数据并保存
      allFeedbackData.value = generateMockData()
      saveDataToStorage()
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    allFeedbackData.value = generateMockData()
  }
}

// 保存数据到本地存储
const saveDataToStorage = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(allFeedbackData.value))
  } catch (error) {
    console.error('保存数据失败:', error)
  }
}

// 查询
const onSearch = async () => {
  try {
    loading.value = true
    
    // 模拟搜索延迟，增加真实性
    await new Promise(resolve => setTimeout(resolve, 800))
    
    pagination.page = 1
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const onReset = () => {
  searchForm.value = { title: '', status: '' }
  pagination.page = 1
}

// 表格操作点击事件
const onTableClickButton = ({ row, btn }: any) => {
  const item = allFeedbackData.value.find(item => item.id === row.id)
  if (!item) return

  switch (btn.code) {
    case 'view':
      onViewReport(item)
      break
    case 'viewDetail':
      onViewDetail(item)
      break
    case 'export':
      onExportReport(item)
      break
    case 'print':
      onPrintReport(item)
      break
    case 'share':
      onShareReport(item)
      break
    case 'viewReply':
      onViewReply(item)
      break
  }
}

// 创建问题反馈表单相关
const showCreateDialog = ref(false)
const createFormRef = ref()
const createForm = ref({
  title: '',
  content: '',
  category: '',
  priority: 'low',
  timeLimit: '',
  attachments: [] as Array<{ name: string; size: number; type: string; url: string }>
})



// 创建表单校验规则
const createFormRules = {
  title: [{ required: true, message: '请输入问题标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入问题描述', trigger: 'blur' }],
  category: [{ required: true, message: '请选择任务分类', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  timeLimit: [{ required: true, message: '请选择任务时限', trigger: 'change' }]
}

// 创建问题反馈表单
const onCreateFeedback = () => {
  // 重置表单
  createForm.value = {
    title: '',
    content: '',
    category: '',
    priority: 'low',
    timeLimit: '',
    attachments: []
  }
  showCreateDialog.value = true
}

// 确认创建
const onConfirmCreate = async () => {
  try {
    // 表单验证
    if (!createForm.value.title.trim()) {
      ElMessage.error('请输入问题标题')
      return
    }
    if (!createForm.value.content.trim()) {
      ElMessage.error('请输入问题描述')
      return
    }
    if (!createForm.value.category) {
      ElMessage.error('请选择任务分类')
      return
    }
    if (!createForm.value.timeLimit) {
      ElMessage.error('请选择任务时限')
      return
    }

    // 生成新的问题反馈
    const newFeedback: FeedbackItem = {
      id: `feedback_${Date.now()}`,
      index: allFeedbackData.value.length + 1,
      title: createForm.value.title,
      content: createForm.value.content,
      submitter: '当前用户', // 实际应用中应该从用户信息获取
      submitTime: new Date().toLocaleString(),
      status: 'pending',
      priority: createForm.value.priority as 'low' | 'medium' | 'high',
      category: createForm.value.category,
      estimatedTime: `${Math.floor(Math.random() * 48) + 1}小时`,
      replyContent: undefined,
      replyTime: undefined
    }

    // 添加到数据列表
    allFeedbackData.value.unshift(newFeedback)

    // 保存到本地存储
    saveDataToStorage()

    // 关闭对话框
    showCreateDialog.value = false

    // 显示成功消息
    ElMessage.success('问题反馈创建成功！')

  } catch (error) {
    console.error('创建问题反馈失败:', error)
    ElMessage.error('创建失败，请重试')
  }
}

// 取消创建
const onCancelCreate = () => {
  showCreateDialog.value = false
}

// 文件上传处理
const handleFileUpload = (file: File) => {
  // 检查文件大小（最大10MB）
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }

  // 检查文件类型
  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain'
  ]

  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('不支持的文件类型，请上传图片、PDF、Word、Excel或文本文件')
    return false
  }

  // 创建文件URL（用于预览）
  const fileUrl = URL.createObjectURL(file)

  // 添加到附件列表
  const attachment = {
    name: file.name,
    size: file.size,
    type: file.type,
    url: fileUrl
  }

  createForm.value.attachments.push(attachment)
  ElMessage.success(`文件 "${file.name}" 上传成功`)

  return false // 阻止自动上传
}

// 移除附件
const removeAttachment = (index: number) => {
  const attachment = createForm.value.attachments[index]
  // 释放URL对象
  URL.revokeObjectURL(attachment.url)
  createForm.value.attachments.splice(index, 1)
  ElMessage.info('附件已移除')
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}



// 问题反馈统计分析相关
const showStatisticsDialog = ref(false)

// 统计数据计算
const statisticsData = computed(() => {
  const data = allFeedbackData.value
  const total = data.length

  // 状态统计
  const statusStats = {
    pending: data.filter(item => item.status === 'pending').length,
    replied: data.filter(item => item.status === 'replied').length,
    timeout: data.filter(item => item.status === 'timeout').length,
    resolved: data.filter(item => item.status === 'resolved').length
  }

  // 优先级统计
  const priorityStats = {
    low: data.filter(item => item.priority === 'low').length,
    medium: data.filter(item => item.priority === 'medium').length,
    high: data.filter(item => item.priority === 'high').length
  }

  // 分类统计
  const categoryStats = data.reduce((acc, item) => {
    acc[item.category] = (acc[item.category] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // 计算满意度评分（模拟数据）
  const satisfactionScore = 3.7

  // 计算各类问题占比
  const systemIssuePercent = Math.round((statusStats.pending / total) * 100)
  const businessIssuePercent = Math.round((statusStats.replied / total) * 100)
  const usabilityIssuePercent = Math.round((statusStats.timeout / total) * 100)

  return {
    total,
    statusStats,
    priorityStats,
    categoryStats,
    satisfactionScore,
    systemIssuePercent,
    businessIssuePercent,
    usabilityIssuePercent
  }
})

// 问题反馈统计分析
const onStatisticsAnalysis = () => {
  showStatisticsDialog.value = true
}

// 关闭统计分析对话框
const onCloseStatistics = () => {
  showStatisticsDialog.value = false
}

// 导出统计报告
const onExportStatistics = () => {
  ElMessage.success('统计报告导出成功！')
}

// 查看问题反馈表单相关
const showViewDialog = ref(false)
const currentViewItem = ref<FeedbackItem | null>(null)

// 查看问题反馈表单
const onViewReport = (item: FeedbackItem) => {
  currentViewItem.value = item
  showViewDialog.value = true
}

// 关闭查看对话框
const onCloseView = () => {
  showViewDialog.value = false
  currentViewItem.value = null
}

// 问题详情查看相关
const showDetailDialog = ref(false)
const currentDetailItem = ref<FeedbackItem | null>(null)

// 查看问题详情
const onViewDetail = (item: FeedbackItem) => {
  currentDetailItem.value = item
  showDetailDialog.value = true
}

// 关闭问题详情对话框
const onCloseDetail = () => {
  showDetailDialog.value = false
  currentDetailItem.value = null
}

// 查看回复相关
const showReplyDialog = ref(false)
const currentReplyItem = ref<FeedbackItem | null>(null)
const replyForm = ref({
  taskName: '',
  problemFeedback: '',
  isSolved: true,
  additionalReply: '',
  satisfaction: 3
})

// 查看回复
const onViewReply = (item: FeedbackItem) => {
  currentReplyItem.value = item
  // 初始化回复表单
  replyForm.value = {
    taskName: item.title,
    problemFeedback: item.content,
    isSolved: item.status === 'resolved',
    additionalReply: item.replyContent || '',
    satisfaction: 3
  }
  showReplyDialog.value = true
}

// 关闭回复对话框
const onCloseReply = () => {
  showReplyDialog.value = false
  currentReplyItem.value = null
}

// 提交回复
const onSubmitReply = () => {
  if (currentReplyItem.value) {
    // 更新问题状态
    const item = currentReplyItem.value
    item.replyContent = replyForm.value.additionalReply
    item.replyTime = new Date().toLocaleString()
    item.status = replyForm.value.isSolved ? 'resolved' : 'replied'

    // 保存到本地存储
    saveDataToStorage()

    ElMessage.success('回复提交成功！')
    onCloseReply()
  }
}

// 导出单个报告
const onExportReport = (item: FeedbackItem) => {
  // 创建报告内容
  const reportContent = generateReportContent(item)

  // 创建下载链接
  const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `问题反馈报告_${item.title}_${new Date().toLocaleDateString()}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  ElMessage.success(`报告"${item.title}"导出成功！`)
}

// 打印报告
const onPrintReport = (item: FeedbackItem) => {
  const reportContent = generateReportContent(item)

  // 创建打印窗口
  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(`
      <html>
        <head>
          <title>问题反馈报告 - ${item.title}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            h1 { color: #333; border-bottom: 2px solid #409eff; padding-bottom: 10px; }
            h2 { color: #666; margin-top: 20px; }
            .info-item { margin: 10px 0; }
            .label { font-weight: bold; color: #333; }
            .content { margin-left: 20px; }
            .status { padding: 4px 8px; border-radius: 4px; color: white; }
            .status-pending { background: #e6a23c; }
            .status-replied { background: #67c23a; }
            .status-timeout { background: #f56c6c; }
            .status-resolved { background: #409eff; }
          </style>
        </head>
        <body>
          <h1>问题反馈报告</h1>
          ${reportContent.replace(/\n/g, '<br>')}
        </body>
      </html>
    `)
    printWindow.document.close()
    printWindow.print()
  }

  ElMessage.success(`报告"${item.title}"已发送到打印机！`)
}

// 分享报告
const onShareReport = (item: FeedbackItem) => {
  // 创建分享链接
  const shareUrl = `${window.location.origin}/reportIntegrationSpotCheck/problemFeedback/share/${item.id}`

  // 状态映射
  const statusMap = {
    pending: '待处理',
    replied: '已回复',
    timeout: '已超时',
    resolved: '已解决'
  }

  const priorityMap = {
    low: '低',
    medium: '中',
    high: '高'
  }

  // 创建分享内容
  const shareContent = `问题反馈分享

问题标题: ${item.title}
提交人: ${item.submitter}
提交时间: ${item.submitTime}
问题分类: ${item.category}
优先级: ${priorityMap[item.priority]}
当前状态: ${statusMap[item.status]}
预计剩余时间: ${item.estimatedTime}

查看详细内容: ${shareUrl}

问题描述:
${item.content}

${item.replyContent ? `处理记录:\n回复时间: ${item.replyTime}\n回复内容: ${item.replyContent}` : '暂无处理记录'}

生成时间: ${new Date().toLocaleString()}`

  // 检查是否支持Web Share API
  if (navigator.share) {
    navigator.share({
      title: `问题反馈 - ${item.title}`,
      text: shareContent,
      url: shareUrl
    }).then(() => {
      ElMessage.success('分享成功')
    }).catch((error) => {
      console.error('分享失败:', error)
      fallbackShare(shareContent, shareUrl)
    })
  } else {
    fallbackShare(shareContent, shareUrl)
  }
}

// 备用分享方法
const fallbackShare = (content: string, url: string) => {
  // 复制到剪贴板
  if (navigator.clipboard) {
    navigator.clipboard.writeText(content).then(() => {
      ElMessage.success('分享内容已复制到剪贴板')
    }).catch(() => {
      // 如果剪贴板API失败，使用传统方法
      legacyCopyToClipboard(content)
    })
  } else {
    legacyCopyToClipboard(content)
  }
}

// 传统复制方法
const legacyCopyToClipboard = (text: string) => {
  const textArea = document.createElement('textarea')
  textArea.value = text
  textArea.style.position = 'fixed'
  textArea.style.left = '-999999px'
  textArea.style.top = '-999999px'
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()

  try {
    document.execCommand('copy')
    ElMessage.success('分享内容已复制到剪贴板')
  } catch (err) {
    ElMessage.error('复制失败，请手动复制分享链接')
    console.error('复制失败:', err)
  }

  document.body.removeChild(textArea)
}

// 生成报告内容
const generateReportContent = (item: FeedbackItem) => {
  const statusMap = {
    pending: '待处理',
    replied: '已回复',
    timeout: '已超时',
    resolved: '已解决'
  }

  const priorityMap = {
    low: '低',
    medium: '中',
    high: '高'
  }

  return `
问题反馈报告
==========================================

基本信息：
- 问题标题：${item.title}
- 提交人：${item.submitter}
- 提交时间：${item.submitTime}
- 问题分类：${item.category}
- 优先级：${priorityMap[item.priority]}
- 当前状态：${statusMap[item.status]}
- 预计剩余时间：${item.estimatedTime}

问题详情：
${item.content}

处理记录：
${item.replyContent ? `- 回复时间：${item.replyTime}\n- 回复内容：${item.replyContent}` : '- 暂无处理记录'}

报告生成时间：${new Date().toLocaleString()}
==========================================
  `.trim()
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1
  }
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 返回上一页
const handleGoBack = () => {
  router.push('/reportIntegrationSpotCheck')
}

// 获取状态显示文本和类型
const getStatusDisplay = (status: string) => {
  const statusMap = {
    'pending': { text: '未回复', type: 'warning' as const },
    'replied': { text: '已回复', type: 'success' as const },
    'timeout': { text: '已超时', type: 'danger' as const },
    'resolved': { text: '已解决', type: 'success' as const }
  }
  return statusMap[status as keyof typeof statusMap] || { text: status, type: 'info' as const }
}

// 获取优先级显示文本和类型
const getPriorityDisplay = (priority: string) => {
  const priorityMap = {
    'low': { text: '低', type: 'info' as const },
    'medium': { text: '中', type: 'warning' as const },
    'high': { text: '高', type: 'danger' as const }
  }
  return priorityMap[priority as keyof typeof priorityMap] || { text: priority, type: 'info' as const }
}

// 组件挂载时加载数据
onMounted(() => {
  loadDataFromStorage()
})
</script>

<template>
  <div class="problem-feedback">
    <Block
      title="问题反馈"
      :enable-fixed-height="true"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button
          size="small"
          type="default"
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>

        <el-button size="small" type="primary" @click="onCreateFeedback">
          创建问题反馈表单
        </el-button>
        <el-button size="small" type="success" @click="onStatisticsAnalysis">
          问题反馈统计分析
        </el-button>
      </template>
      
      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="3"
            :label-width="74"
            :enable-reset="true"
            confirm-text="查询"
            reset-text="重置"
            button-vertical="flowing"
            @submit="onSearch"
            @reset="onReset"
          />
        </div>
      </template>
      
      <!-- 列表 -->
      <TableV2
        ref="tableRef"
        :defaultTableData="paginatedData"
        :columns="columns"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="false"
        :height="tableHeight"
        :buttons="[]"
        :loading="loading"
        @click-button="onTableClickButton"
      >
        <!-- 问题详情列自定义显示 -->
        <template #content="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="onTableClickButton({ row, btn: { code: 'viewDetail' } })"
          >
            查看
          </el-button>
        </template>

        <!-- 优先级列自定义显示 -->
        <template #priority="{ row }">
          <el-tag :type="getPriorityDisplay(row.priority).type" size="small">
            {{ getPriorityDisplay(row.priority).text }}
          </el-tag>
        </template>

        <!-- 状态列自定义显示 -->
        <template #status="{ row }">
          <el-tag :type="getStatusDisplay(row.status).type" size="small">
            {{ getStatusDisplay(row.status).text }}
          </el-tag>
        </template>

        <!-- 生成报告列自定义显示 -->
        <template #report="{ row }">
          <div class="report-actions">
            <el-button type="primary" size="small" @click="onTableClickButton({ row, btn: { code: 'view' } })">
              查看
            </el-button>
            <el-button type="success" size="small" @click="onTableClickButton({ row, btn: { code: 'export' } })">
              导出
            </el-button>
            <el-button type="info" size="small" @click="onTableClickButton({ row, btn: { code: 'print' } })">
              打印
            </el-button>
            <el-button type="warning" size="small" @click="onTableClickButton({ row, btn: { code: 'share' } })">
              分享
            </el-button>
          </div>
        </template>

        <!-- 操作列自定义显示 -->
        <template #action="{ row }">
          <div class="action-buttons">
            <!-- 状态相关按钮：只有已回复和已解决状态才显示查看回复按钮 -->
            <el-button
              v-if="row.status === 'replied' || row.status === 'resolved'"
              type="success"
              size="small"
              @click="onTableClickButton({ row, btn: { code: 'viewReply' } })"
            >
              查看回复
            </el-button>
            <span v-else class="no-action">-</span>
          </div>
        </template>
      </TableV2>
      
      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      />
    </Block>

    <!-- 创建问题反馈表单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建问题反馈表单"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createFormRules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="问题标题" prop="title" required>
          <el-input
            v-model="createForm.title"
            placeholder="请输入问题标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="问题描述" prop="content" required>
          <el-input
            v-model="createForm.content"
            type="textarea"
            placeholder="请详细描述您遇到的问题"
            :rows="4"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="任务分类" prop="category" required>
          <el-select
            v-model="createForm.category"
            placeholder="系统功能分类"
            style="width: 100%"
          >
            <el-option label="系统问题" value="系统问题" />
            <el-option label="功能建议" value="功能建议" />
            <el-option label="性能优化" value="性能优化" />
            <el-option label="安全问题" value="安全问题" />
            <el-option label="用户体验" value="用户体验" />
            <el-option label="数据问题" value="数据问题" />
          </el-select>
        </el-form-item>

        <el-form-item label="优先级" prop="priority" required>
          <el-radio-group v-model="createForm.priority">
            <el-radio value="low">低</el-radio>
            <el-radio value="medium">中</el-radio>
            <el-radio value="high">高</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="任务时限" prop="timeLimit" required>
          <el-select
            v-model="createForm.timeLimit"
            placeholder="小于0.5小时"
            style="width: 100%"
          >
            <el-option label="小于0.5小时" value="小于0.5小时" />
            <el-option label="0.5-1小时" value="0.5-1小时" />
            <el-option label="1-2小时" value="1-2小时" />
            <el-option label="2-4小时" value="2-4小时" />
            <el-option label="4-8小时" value="4-8小时" />
            <el-option label="大于8小时" value="大于8小时" />
          </el-select>
        </el-form-item>

        <el-form-item label="上传附件">
          <div class="upload-section">
            <el-upload
              :before-upload="handleFileUpload"
              :show-file-list="false"
              multiple
              accept=".jpg,.jpeg,.png,.gif,.webp,.pdf,.doc,.docx,.xls,.xlsx,.txt"
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon>
                点击上传
              </el-button>
            </el-upload>
            <div class="upload-tip">
              支持上传图片、PDF、Word、Excel、文本文件，最大10MB
            </div>

            <!-- 附件列表 -->
            <div v-if="createForm.attachments.length > 0" class="attachment-list">
              <div
                v-for="(attachment, index) in createForm.attachments"
                :key="index"
                class="attachment-item"
              >
                <div class="attachment-info">
                  <el-icon class="attachment-icon">
                    <Document v-if="attachment.type.includes('pdf')" />
                    <Picture v-else-if="attachment.type.includes('image')" />
                    <Document v-else />
                  </el-icon>
                  <div class="attachment-details">
                    <div class="attachment-name">{{ attachment.name }}</div>
                    <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
                  </div>
                </div>
                <el-button
                  type="danger"
                  size="small"
                  @click="removeAttachment(index)"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onCancelCreate">取消</el-button>
          <el-button type="primary" @click="onConfirmCreate">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 问题反馈统计分析对话框 -->
    <el-dialog
      v-model="showStatisticsDialog"
      title="问题反馈统计分析"
      width="1000px"
      :close-on-click-modal="false"
      class="statistics-dialog"
    >
      <div class="statistics-content">
        <!-- 第一行：各类问题占比 + 反馈满意度统计 -->
        <div class="statistics-row">
          <div class="statistics-card">
            <h3>1. 各类问题占比</h3>
            <div class="pie-chart-container">
              <div class="pie-chart">
                <div class="pie-slice slice-1" :style="{ '--percentage': statisticsData.systemIssuePercent }"></div>
                <div class="pie-slice slice-2" :style="{ '--percentage': statisticsData.businessIssuePercent }"></div>
                <div class="pie-slice slice-3" :style="{ '--percentage': statisticsData.usabilityIssuePercent }"></div>
              </div>
              <div class="pie-legend">
                <div class="legend-item">
                  <span class="legend-color color-1"></span>
                  <span>系统功能问题 {{ statisticsData.systemIssuePercent }}%</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color color-2"></span>
                  <span>专业领域问题 {{ statisticsData.businessIssuePercent }}%</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color color-3"></span>
                  <span>使用友好问题 {{ statisticsData.usabilityIssuePercent }}%</span>
                </div>
              </div>
            </div>
          </div>

          <div class="statistics-card">
            <h3>4. 反馈满意度统计</h3>
            <div class="satisfaction-container">
              <div class="satisfaction-score">
                <span class="score-label">平均满意度评分：</span>
                <span class="score-value">{{ statisticsData.satisfactionScore }}</span>
              </div>
              <div class="satisfaction-chart">
                <div class="chart-label">满意度分布：任务数</div>
                <div class="bar-chart">
                  <div class="bar bar-1" style="height: 60px;"></div>
                  <div class="bar bar-2" style="height: 80px;"></div>
                  <div class="bar bar-3" style="height: 100px;"></div>
                  <div class="bar bar-4" style="height: 90px;"></div>
                  <div class="bar bar-5" style="height: 70px;"></div>
                </div>
                <div class="chart-labels">
                  <span>一星</span>
                  <span>二星</span>
                  <span>三星</span>
                  <span>四星</span>
                  <span>五星</span>
                </div>
              </div>
              <div class="satisfaction-summary">
                <div>四五星中，主要问题类型为 <span class="highlight">系统功能问题</span></div>
                <div>一二星中，主要问题类型为 <span class="highlight">专业领域问题</span></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二行：未解决问题 + 处理时间 -->
        <div class="statistics-row">
          <div class="statistics-card">
            <h3>2. 未解决问题</h3>
            <div class="progress-container">
              <div class="progress-item">
                <span class="progress-label">总反馈数：</span>
                <span class="progress-value">{{ statisticsData.total }}</span>
              </div>
              <div class="progress-item">
                <span class="progress-label">已解决问题数：</span>
                <span class="progress-value">{{ statisticsData.statusStats.resolved }}</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: (statisticsData.statusStats.resolved / statisticsData.total * 100) + '%' }"></div>
              </div>
              <div class="progress-percentage">{{ Math.round(statisticsData.statusStats.resolved / statisticsData.total * 100) }}%</div>
            </div>
          </div>

          <div class="statistics-card">
            <h3>3. 处理时间</h3>
            <div class="progress-container">
              <div class="progress-item">
                <span class="progress-label">总反馈数：</span>
                <span class="progress-value">{{ statisticsData.total }}</span>
              </div>
              <div class="progress-item">
                <span class="progress-label">已超时任务数：</span>
                <span class="progress-value">{{ statisticsData.statusStats.timeout }}</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill timeout" :style="{ width: (statisticsData.statusStats.timeout / statisticsData.total * 100) + '%' }"></div>
              </div>
              <div class="progress-percentage">{{ Math.round(statisticsData.statusStats.timeout / statisticsData.total * 100) }}%</div>
            </div>
          </div>
        </div>

        <!-- 第三行：反馈趋势时间变化 -->
        <div class="statistics-row">
          <div class="statistics-card full-width">
            <h3>5. 反馈趋势时间变化</h3>
            <div class="trend-chart">
              <div class="trend-y-axis">
                <span>问题反馈任务数</span>
              </div>
              <div class="trend-line-container">
                <svg class="trend-svg" viewBox="0 0 400 150">
                  <polyline
                    points="20,120 60,100 100,110 140,90 180,70 220,85 260,75 300,80 340,85 380,90"
                    fill="none"
                    stroke="#409eff"
                    stroke-width="2"
                  />
                  <circle cx="20" cy="120" r="3" fill="#409eff" />
                  <circle cx="60" cy="100" r="3" fill="#409eff" />
                  <circle cx="100" cy="110" r="3" fill="#409eff" />
                  <circle cx="140" cy="90" r="3" fill="#409eff" />
                  <circle cx="180" cy="70" r="3" fill="#409eff" />
                  <circle cx="220" cy="85" r="3" fill="#409eff" />
                  <circle cx="260" cy="75" r="3" fill="#409eff" />
                  <circle cx="300" cy="80" r="3" fill="#409eff" />
                  <circle cx="340" cy="85" r="3" fill="#409eff" />
                  <circle cx="380" cy="90" r="3" fill="#409eff" />
                </svg>
              </div>
              <div class="trend-x-axis">
                <span>时间</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onCloseStatistics">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看问题反馈表单对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="查看问题反馈表单"
      width="600px"
      :close-on-click-modal="false"
      class="view-dialog"
    >
      <el-form
        v-if="currentViewItem"
        :model="currentViewItem"
        label-width="100px"
        label-position="left"
        class="view-form"
      >
        <el-form-item label="问题标题：">
          <el-input
            v-model="currentViewItem.title"
            placeholder="请输入问题标题"
            readonly
          />
        </el-form-item>

        <el-form-item label="问题描述：">
          <el-input
            v-model="currentViewItem.content"
            type="textarea"
            placeholder="请详细描述您遇到的问题"
            :rows="4"
            readonly
          />
        </el-form-item>

        <el-form-item label="任务分类：">
          <el-select
            v-model="currentViewItem.category"
            placeholder="系统功能分类"
            style="width: 100%"
            disabled
          >
            <el-option label="系统问题" value="系统问题" />
            <el-option label="功能建议" value="功能建议" />
            <el-option label="性能优化" value="性能优化" />
            <el-option label="安全问题" value="安全问题" />
            <el-option label="用户体验" value="用户体验" />
            <el-option label="数据问题" value="数据问题" />
          </el-select>
        </el-form-item>

        <el-form-item label="优先级：">
          <el-radio-group v-model="currentViewItem.priority" disabled>
            <el-radio value="low">低</el-radio>
            <el-radio value="medium">中</el-radio>
            <el-radio value="high">高</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="任务时限：">
          <el-select
            v-model="currentViewItem.estimatedTime"
            placeholder="小于0.5小时"
            style="width: 100%"
            disabled
          >
            <el-option label="小于0.5小时" value="小于0.5小时" />
            <el-option label="0.5-1小时" value="0.5-1小时" />
            <el-option label="1-2小时" value="1-2小时" />
            <el-option label="2-4小时" value="2-4小时" />
            <el-option label="4-8小时" value="4-8小时" />
            <el-option label="大于8小时" value="大于8小时" />
          </el-select>
        </el-form-item>

        <el-form-item label="上传附件：">
          <div class="attachment-display">
            <span class="attachment-link">附件.pdf</span>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onCloseView">取消</el-button>
          <el-button type="primary" @click="onCloseView">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 问题详情查看对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="问题详情"
      width="700px"
      :close-on-click-modal="false"
      class="detail-dialog"
    >
      <div v-if="currentDetailItem" class="detail-content">
        <!-- 问题基本信息 -->
        <div class="detail-header">
          <h3>{{ currentDetailItem.title }}</h3>
          <div class="detail-meta">
            <el-tag :type="getStatusDisplay(currentDetailItem.status).type" size="small">
              {{ getStatusDisplay(currentDetailItem.status).text }}
            </el-tag>
            <el-tag :type="getPriorityDisplay(currentDetailItem.priority).type" size="small" style="margin-left: 8px;">
              {{ getPriorityDisplay(currentDetailItem.priority).text }}
            </el-tag>
          </div>
        </div>

        <!-- 详细信息卡片 -->
        <div class="detail-cards">
          <!-- 基本信息卡片 -->
          <div class="detail-card">
            <div class="card-title">
              <el-icon><InfoFilled /></el-icon>
              基本信息
            </div>
            <div class="card-content">
              <div class="info-row">
                <span class="info-label">提交人：</span>
                <span class="info-value">{{ currentDetailItem.submitter }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">提交时间：</span>
                <span class="info-value">{{ currentDetailItem.submitTime }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">问题分类：</span>
                <span class="info-value">{{ currentDetailItem.category }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">预计剩余时间：</span>
                <span class="info-value">{{ currentDetailItem.estimatedTime }}</span>
              </div>
            </div>
          </div>

          <!-- 问题描述卡片 -->
          <div class="detail-card">
            <div class="card-title">
              <el-icon><Document /></el-icon>
              问题描述
            </div>
            <div class="card-content">
              <div class="problem-description">
                {{ currentDetailItem.content }}
              </div>
            </div>
          </div>

          <!-- 处理记录卡片 -->
          <div class="detail-card">
            <div class="card-title">
              <el-icon><Clock /></el-icon>
              处理记录
            </div>
            <div class="card-content">
              <div v-if="currentDetailItem.replyContent" class="reply-info">
                <div class="reply-time">
                  回复时间：{{ currentDetailItem.replyTime }}
                </div>
                <div class="reply-content">
                  {{ currentDetailItem.replyContent }}
                </div>
              </div>
              <div v-else class="no-reply">
                <el-icon><InfoFilled /></el-icon>
                暂无处理记录
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onCloseDetail">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看回复对话框 -->
    <el-dialog
      v-model="showReplyDialog"
      title="查看回复问题"
      width="500px"
      :close-on-click-modal="false"
      class="reply-dialog"
    >
      <el-form
        v-if="currentReplyItem"
        :model="replyForm"
        label-width="100px"
        label-position="left"
        class="reply-form"
      >
        <el-form-item label="任务名称：">
          <el-input
            v-model="replyForm.taskName"
            placeholder="请输入任务名称"
            readonly
          />
        </el-form-item>

        <el-form-item label="问题反馈：">
          <el-input
            v-model="replyForm.problemFeedback"
            type="textarea"
            placeholder="请输入问题反馈"
            :rows="3"
            readonly
          />
        </el-form-item>

        <el-form-item label="是否解决问题：">
          <el-radio-group v-model="replyForm.isSolved">
            <el-radio :value="true">是</el-radio>
            <el-radio :value="false">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="追加回复：">
          <el-input
            v-model="replyForm.additionalReply"
            type="textarea"
            placeholder="请输入追加回复内容"
            :rows="4"
          />
        </el-form-item>

        <el-form-item label="满意度评价：">
          <div class="satisfaction-rating">
            <el-rate
              v-model="replyForm.satisfaction"
              :max="5"
              :colors="['#F7BA2A', '#F7BA2A', '#F7BA2A']"
              :icons="['Star', 'Star', 'Star']"
              show-text
              :texts="['很不满意', '不满意', '一般', '满意', '很满意']"
            />
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onCloseReply">取消</el-button>
          <el-button type="primary" @click="onSubmitReply">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<route>
{
  meta: {
    title: '问题反馈',
  },
}
</route>

<style scoped lang="scss">
.problem-feedback {
  .search {
    margin-bottom: 16px;
  }

  .report-actions {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    justify-content: center;

    .el-button {
      margin: 0;
      font-size: 12px;
      padding: 4px 8px;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 32px;

    .no-action {
      color: #999;
      font-size: 14px;
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .report-actions {
      .el-button {
        font-size: 11px;
        padding: 3px 6px;
      }
    }
  }

  @media (max-width: 768px) {
    .search {
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 12px;
        }
      }
    }

    .report-actions {
      flex-direction: column;
      gap: 2px;

      .el-button {
        font-size: 10px;
        padding: 2px 4px;
        width: 100%;
      }
    }
  }

  // 对话框样式
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 20px 20px 10px;
      border-bottom: 1px solid #ebeef5;

      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 10px 20px 20px;
      border-top: 1px solid #ebeef5;

      .dialog-footer {
        text-align: right;

        .el-button {
          margin-left: 10px;
        }
      }
    }
  }

  :deep(.el-form) {
    .el-form-item {
      margin-bottom: 20px;

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }

      .el-input__inner,
      .el-textarea__inner {
        border-radius: 4px;
      }

      .el-radio-group {
        .el-radio {
          margin-right: 20px;
        }
      }
    }
  }

  // 文件上传样式
  .upload-section {
    .upload-tip {
      margin-top: 8px;
      color: #999;
      font-size: 12px;
    }

    .attachment-list {
      margin-top: 16px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 12px;
      background-color: #fafafa;

      .attachment-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }

        .attachment-info {
          display: flex;
          align-items: center;
          flex: 1;

          .attachment-icon {
            font-size: 20px;
            color: #409eff;
            margin-right: 12px;
          }

          .attachment-details {
            .attachment-name {
              font-size: 14px;
              color: #303133;
              margin-bottom: 4px;
            }

            .attachment-size {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }
  }

  :deep(.el-upload) {
    .el-upload__input {
      display: none !important;
    }
  }

  // 统计分析对话框样式
  :deep(.statistics-dialog) {
    .el-dialog__body {
      padding: 20px;
      max-height: 70vh;
      overflow-y: auto;
    }
  }

  .statistics-content {
    .statistics-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .statistics-card {
      flex: 1;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      border: 1px solid #e9ecef;

      &.full-width {
        flex: none;
        width: 100%;
      }

      h3 {
        margin: 0 0 20px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    // 饼图样式
    .pie-chart-container {
      display: flex;
      align-items: center;
      gap: 30px;
    }

    .pie-chart {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: conic-gradient(
        #409eff 0deg 61deg,
        #67c23a 61deg 79deg,
        #e6a23c 79deg 100deg,
        #f56c6c 100deg 360deg
      );
      position: relative;
    }

    .pie-legend {
      .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
          margin-right: 8px;

          &.color-1 { background: #409eff; }
          &.color-2 { background: #67c23a; }
          &.color-3 { background: #e6a23c; }
        }
      }
    }

    // 满意度样式
    .satisfaction-container {
      .satisfaction-score {
        margin-bottom: 20px;

        .score-label {
          font-size: 14px;
          color: #606266;
        }

        .score-value {
          font-size: 24px;
          font-weight: bold;
          color: #409eff;
          margin-left: 8px;
        }
      }

      .satisfaction-chart {
        margin-bottom: 20px;

        .chart-label {
          font-size: 12px;
          color: #909399;
          margin-bottom: 10px;
        }

        .bar-chart {
          display: flex;
          align-items: end;
          gap: 8px;
          height: 100px;
          margin-bottom: 8px;

          .bar {
            flex: 1;
            border-radius: 4px 4px 0 0;

            &.bar-1 { background: #409eff; }
            &.bar-2 { background: #67c23a; }
            &.bar-3 { background: #e6a23c; }
            &.bar-4 { background: #f56c6c; }
            &.bar-5 { background: #909399; }
          }
        }

        .chart-labels {
          display: flex;
          gap: 8px;

          span {
            flex: 1;
            text-align: center;
            font-size: 12px;
            color: #909399;
          }
        }
      }

      .satisfaction-summary {
        font-size: 12px;
        color: #606266;

        div {
          margin-bottom: 4px;
        }

        .highlight {
          color: #409eff;
          font-weight: 500;
        }
      }
    }

    // 进度条样式
    .progress-container {
      .progress-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        font-size: 14px;

        .progress-label {
          color: #606266;
        }

        .progress-value {
          font-weight: 500;
          color: #303133;
        }
      }

      .progress-bar {
        width: 100%;
        height: 20px;
        background: #f0f2f5;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 8px;

        .progress-fill {
          height: 100%;
          background: #409eff;
          border-radius: 10px;
          transition: width 0.3s ease;

          &.timeout {
            background: #f56c6c;
          }
        }
      }

      .progress-percentage {
        text-align: right;
        font-size: 14px;
        font-weight: 500;
        color: #409eff;
      }
    }

    // 趋势图样式
    .trend-chart {
      position: relative;
      height: 200px;

      .trend-y-axis {
        position: absolute;
        left: 0;
        top: 50%;
        transform: rotate(-90deg) translateY(-50%);
        transform-origin: left center;
        font-size: 12px;
        color: #909399;
      }

      .trend-line-container {
        margin-left: 40px;
        margin-right: 20px;
        height: 150px;
        border-left: 1px solid #e4e7ed;
        border-bottom: 1px solid #e4e7ed;
        position: relative;

        .trend-svg {
          width: 100%;
          height: 100%;
        }
      }

      .trend-x-axis {
        text-align: center;
        margin-top: 10px;
        font-size: 12px;
        color: #909399;
      }
    }
  }

  // 查看问题反馈表单对话框样式
  :deep(.view-dialog) {
    .el-dialog__body {
      padding: 20px;
      max-height: 70vh;
      overflow-y: auto;
    }
  }

  .view-form {
    .el-form-item {
      margin-bottom: 20px;

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }

      .el-input__inner,
      .el-textarea__inner {
        border-radius: 4px;
        background-color: #f5f7fa;
      }

      .el-input.is-disabled .el-input__inner,
      .el-textarea.is-disabled .el-textarea__inner {
        background-color: #f5f7fa;
        border-color: #e4e7ed;
        color: #606266;
      }

      .el-select.is-disabled .el-input__inner {
        background-color: #f5f7fa;
        border-color: #e4e7ed;
        color: #606266;
      }

      .el-radio-group {
        .el-radio.is-disabled {
          .el-radio__label {
            color: #606266;
          }
        }
      }
    }

    .attachment-display {
      .attachment-link {
        color: #409eff;
        text-decoration: underline;
        cursor: pointer;

        &:hover {
          color: #66b1ff;
        }
      }
    }
  }

  // 问题详情查看对话框样式
  :deep(.detail-dialog) {
    .el-dialog__body {
      padding: 20px;
      max-height: 70vh;
      overflow-y: auto;
    }
  }

  .detail-content {
    .detail-header {
      border-bottom: 2px solid #409eff;
      padding-bottom: 16px;
      margin-bottom: 24px;

      h3 {
        margin: 0 0 12px 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }

      .detail-meta {
        display: flex;
        align-items: center;
      }
    }

    .detail-cards {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .detail-card {
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        overflow: hidden;

        .card-title {
          background: #409eff;
          color: white;
          padding: 12px 16px;
          font-weight: 500;
          display: flex;
          align-items: center;

          .el-icon {
            margin-right: 8px;
            font-size: 16px;
          }
        }

        .card-content {
          padding: 16px;

          .info-row {
            display: flex;
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            .info-label {
              font-weight: 500;
              color: #606266;
              min-width: 120px;
            }

            .info-value {
              color: #303133;
              flex: 1;
            }
          }

          .problem-description {
            line-height: 1.6;
            color: #303133;
            background: white;
            padding: 16px;
            border-radius: 6px;
            border-left: 4px solid #409eff;
          }

          .reply-info {
            .reply-time {
              font-size: 14px;
              color: #909399;
              margin-bottom: 12px;
            }

            .reply-content {
              line-height: 1.6;
              color: #303133;
              background: white;
              padding: 16px;
              border-radius: 6px;
              border-left: 4px solid #67c23a;
            }
          }

          .no-reply {
            display: flex;
            align-items: center;
            color: #909399;
            font-style: italic;
            justify-content: center;
            padding: 20px;

            .el-icon {
              margin-right: 8px;
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .detail-content {
      .detail-cards {
        .detail-card {
          .card-content {
            .info-row {
              flex-direction: column;

              .info-label {
                min-width: auto;
                margin-bottom: 4px;
              }
            }
          }
        }
      }
    }
  }


}
</style>
