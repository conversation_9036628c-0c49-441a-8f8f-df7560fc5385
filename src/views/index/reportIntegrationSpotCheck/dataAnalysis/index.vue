<script setup lang="ts" name="dataAnalysis">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import type {
  DataAnalysisItem,
  PaginationParams,
  TableButton,
  TableColumn
} from './types'
import { DataAnalysisStatus } from './types'
import { dataAnalysisStorage, historyStorage, initializeAllData } from './storage'
import CreateAnalysisDialog from './components/CreateAnalysisDialog.vue'
import HistoryDialog from './components/HistoryDialog.vue'
import AlgorithmDialog from './components/AlgorithmDialog.vue'
import AnalysisResultDialog from './components/AnalysisResultDialog.vue'

// 路由实例
const router = useRouter()

// 初始化数据
initializeAllData()

// 搜索表单配置
const searchFormProp = ref([
  { label: '数据分析ID', prop: 'id', type: 'text' },
  { label: '创建者', prop: 'creator', type: 'text' },
  {
    label: '状态',
    prop: 'status',
    type: 'select',
    options: Object.values(DataAnalysisStatus).map(status => ({ label: status, value: status }))
  },
  { label: '创建时间', prop: 'dateRange', type: 'daterange' }
])

// 搜索表单数据
const searchForm = ref({
  id: '',
  creator: '',
  status: '',
  dateRange: null
})

// 加载状态
const loading = ref(false)
const searchLoading = ref(false)

// 表格高度
const tableHeight = ref(0)
const currentRow = ref<DataAnalysisItem | null>(null)

// 表格列配置
const columns: TableColumn[] = [
  { prop: 'id', label: '数据分析ID',  align: 'center' },
  { prop: 'createTime', label: '创建时间', align: 'center' },
  { prop: 'creator', label: '创建者', align: 'center' },
  { prop: 'progress', label: '进程', align: 'center' },
  { prop: 'status', label: '状态', align: 'center' },
  { prop: 'logs', label: '日志', align: 'center' }
]

// 分页配置
const pagination = reactive<PaginationParams>({
  page: 1,
  size: 10,
  total: 0
})

// 表格数据
const tableData = ref<DataAnalysisItem[]>([])

// 弹窗状态
const showCreateDialog = ref(false)
const showHistoryDialog = ref(false)
const showAlgorithmDialog = ref(false)
const showResultDialog = ref(false)
const editData = ref<DataAnalysisItem | null>(null)
const currentAnalysisData = ref<DataAnalysisItem | null>(null)

// 动态生成操作按钮
const getButtons = (row: DataAnalysisItem): TableButton[] => {
  const buttons: TableButton[] = []
  
  if (row.status === DataAnalysisStatus.COMPLETED) {
    buttons.push(
      { label: '查看', type: 'info', code: 'view' },
      { label: '导出', type: 'success', code: 'export' },
      { label: '打印', type: 'default', code: 'print' },
      { label: '分享', type: 'primary', code: 'share' }
    )
  } else if (row.status === DataAnalysisStatus.RUNNING) {
    buttons.push(
      { label: '暂停', type: 'warning', code: 'pause' },
      { label: '终止', type: 'danger', code: 'terminate', popconfirm: '确认终止该分析任务吗？' }
    )
  } else if (row.status === DataAnalysisStatus.PAUSED) {
    buttons.push(
      { label: '恢复', type: 'success', code: 'resume' },
      { label: '终止', type: 'danger', code: 'terminate', popconfirm: '确认终止该分析任务吗？' }
    )
  } else if (row.status === DataAnalysisStatus.PENDING) {
    buttons.push(
      { label: '启动', type: 'primary', code: 'start' },
      { label: '编辑', type: 'warning', code: 'edit' },
      { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除该分析任务吗？' }
    )
  } else if (row.status === DataAnalysisStatus.FAILED || row.status === DataAnalysisStatus.TERMINATED) {
    buttons.push(
      { label: '重新运行', type: 'primary', code: 'restart' },
      { label: '查看日志', type: 'info', code: 'viewLog' },
      { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除该分析任务吗？' }
    )
  }
  
  return buttons
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  
  try {
    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const filters = {
      id: searchForm.value.id,
      status: searchForm.value.status,
      creator: searchForm.value.creator,
      dateRange: searchForm.value.dateRange
    }
    
    const result = dataAnalysisStorage.getPage(pagination.page, pagination.size, filters)

    // 直接使用数据，不添加序号
    tableData.value = result.data

    pagination.total = result.total
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const onSearch = async () => {
  searchLoading.value = true
  pagination.page = 1
  
  try {
    await loadTableData()
  } finally {
    searchLoading.value = false
  }
}

// 重置搜索
const onReset = () => {
  searchForm.value = {
    id: '',
    status: '',
    creator: '',
    dateRange: null
  }
  onSearch()
}

// 表格操作点击事件
const onTableClickButton = ({ row, btn }: { row: DataAnalysisItem; btn: TableButton }) => {
  currentRow.value = row
  
  switch (btn.code) {
    case 'view':
      handleViewResult(row)
      break
    case 'export':
      handleExport(row)
      break
    case 'print':
      handlePrint(row)
      break
    case 'share':
      handleShare(row)
      break
    case 'pause':
      handlePause(row)
      break
    case 'resume':
      handleResume(row)
      break
    case 'terminate':
      handleTerminate(row)
      break
    case 'start':
      handleStart(row)
      break
    case 'edit':
      handleEdit(row)
      break
    case 'delete':
      handleDelete(row)
      break
    case 'restart':
      handleRestart(row)
      break
    case 'viewLog':
      handleViewLog(row)
      break
  }
}

// 操作处理函数
const handleViewResult = (row: DataAnalysisItem) => {
  currentAnalysisData.value = row
  showResultDialog.value = true
}

const handleExport = (row: DataAnalysisItem) => {
  // 创建导出数据
  const exportData = {
    analysisId: row.id,
    analysisName: row.name,
    creator: row.creator,
    createTime: row.createTime,
    status: row.status,
    progress: row.progress,
    results: {
      dataIntegrationAnalysis: {
        totalRecords: 12345,
        duplicateRecords: 600,
        duplicateRate: '60%'
      },
      dataUsageAnalysis: {
        datasetName: '姓名',
        usageCount: 600,
        usageRate: '60%'
      },
      dataRelationAnalysis: {
        fieldA: '身份证号',
        fieldB: '户籍',
        relationStrength: 4.3
      },
      dataAnomalyAnalysis: {
        abnormalValue: '-3',
        threshold: '0~120',
        source: '合川区养老保险登记表_字段：体重'
      },
      dataClusterAnalysis: {
        clusterId: '003124',
        dataType: '村地面积',
        centerPoint: '-3',
        sampleCount: 500
      },
      dataMissingAnalysis: {
        missingCount: 600,
        missingRate: '60%',
        location: '合川区养老保险登记表 字段'
      },
      dataDuplicateAnalysis: {
        duplicateValue: '2021年3月1日',
        occurrenceCount: 600,
        firstLocation: '报表1',
        lastLocation: '报表5'
      },
      dataConsistencyAnalysis: {
        sourceA: '报表1',
        sourceB: '报表3',
        inconsistentRecords: 212,
        inconsistentFields: '比较源A：年龄 比较源B：年龄'
      },
      dataAccuracyAnalysis: {
        errorRecords: 600,
        errorId: '235456',
        errorDetail: '"生日"字段不能为负',
        suggestion: '重新输入合适范围内的数据'
      },
      dataCompletenessAnalysis: {
        expectedRecords: 235266,
        actualRecords: 129024,
        completenessRate: '60%'
      }
    }
  }

  // 转换为JSON字符串
  const jsonString = JSON.stringify(exportData, null, 2)

  // 创建Blob对象
  const blob = new Blob([jsonString], { type: 'application/json' })

  // 创建下载链接
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `数据分析结果_${row.name}_${new Date().toISOString().slice(0, 10)}.json`

  // 触发下载
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  // 释放URL对象
  URL.revokeObjectURL(url)

  ElMessage.success(`导出分析结果成功: ${row.name}`)
}

const handlePrint = (row: DataAnalysisItem) => {
  // 创建打印内容
  const printContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>数据分析结果 - ${row.name}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .info { font-size: 14px; color: #666; }
        .section { margin-bottom: 25px; }
        .section-title { font-size: 16px; font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
        .data-row { margin-bottom: 8px; }
        .label { font-weight: bold; }
        .value { margin-left: 10px; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="title">数据分析结果报告</div>
        <div class="info">
          <div>分析ID: ${row.id}</div>
          <div>分析名称: ${row.name}</div>
          <div>创建者: ${row.creator}</div>
          <div>创建时间: ${row.createTime}</div>
          <div>打印时间: ${new Date().toLocaleString()}</div>
        </div>
      </div>

      <div class="grid">
        <div class="section">
          <div class="section-title">1. 报表整合率的数据项重复查分析</div>
          <div class="data-row"><span class="label">总记录数:</span><span class="value">12345</span></div>
          <div class="data-row"><span class="label">重复记录数:</span><span class="value">600</span></div>
          <div class="data-row"><span class="label">重复率:</span><span class="value">60%</span></div>
        </div>

        <div class="section">
          <div class="section-title">2. 数据使用率分析</div>
          <div class="data-row"><span class="label">数据集名称:</span><span class="value">姓名</span></div>
          <div class="data-row"><span class="label">使用次数:</span><span class="value">600</span></div>
          <div class="data-row"><span class="label">使用频率:</span><span class="value">60%</span></div>
        </div>

        <div class="section">
          <div class="section-title">3. 数据关联关系统计分析</div>
          <div class="data-row"><span class="label">关联字段A:</span><span class="value">身份证号</span></div>
          <div class="data-row"><span class="label">关联字段B:</span><span class="value">户籍</span></div>
          <div class="data-row"><span class="label">关联强度:</span><span class="value">4.3</span></div>
        </div>

        <div class="section">
          <div class="section-title">4. 数据异常值分析</div>
          <div class="data-row"><span class="label">异常值内容:</span><span class="value">-3</span></div>
          <div class="data-row"><span class="label">阈值范围:</span><span class="value">0~120</span></div>
          <div class="data-row"><span class="label">来源:</span><span class="value">合川区养老保险登记表_字段：体重</span></div>
        </div>

        <div class="section">
          <div class="section-title">5. 数据聚类统计分析</div>
          <div class="data-row"><span class="label">聚类编号:</span><span class="value">003124</span></div>
          <div class="data-row"><span class="label">数据类别:</span><span class="value">村地面积</span></div>
          <div class="data-row"><span class="label">包含样本数:</span><span class="value">500</span></div>
        </div>

        <div class="section">
          <div class="section-title">6. 数据缺失值检测分析</div>
          <div class="data-row"><span class="label">缺失值数量:</span><span class="value">600</span></div>
          <div class="data-row"><span class="label">缺失值占比:</span><span class="value">60%</span></div>
          <div class="data-row"><span class="label">位置:</span><span class="value">合川区养老保险登记表 字段</span></div>
        </div>

        <div class="section">
          <div class="section-title">7. 数据重复值检测分析</div>
          <div class="data-row"><span class="label">重复值:</span><span class="value">2021年3月1日</span></div>
          <div class="data-row"><span class="label">出现次数:</span><span class="value">600</span></div>
          <div class="data-row"><span class="label">首次出现:</span><span class="value">报表1</span></div>
          <div class="data-row"><span class="label">最后出现:</span><span class="value">报表5</span></div>
        </div>

        <div class="section">
          <div class="section-title">8. 数据一致性检测分析</div>
          <div class="data-row"><span class="label">比较源A:</span><span class="value">报表1</span></div>
          <div class="data-row"><span class="label">比较源B:</span><span class="value">报表3</span></div>
          <div class="data-row"><span class="label">不一致记录数:</span><span class="value">212</span></div>
        </div>

        <div class="section">
          <div class="section-title">9. 数据准确性检测分析</div>
          <div class="data-row"><span class="label">错误记录数:</span><span class="value">600</span></div>
          <div class="data-row"><span class="label">错误ID:</span><span class="value">235456</span></div>
          <div class="data-row"><span class="label">错误详情:</span><span class="value">"生日"字段不能为负</span></div>
          <div class="data-row"><span class="label">修正建议:</span><span class="value">重新输入合适范围内的数据</span></div>
        </div>

        <div class="section">
          <div class="section-title">10. 数据完整性检测分析</div>
          <div class="data-row"><span class="label">预期记录数:</span><span class="value">235266</span></div>
          <div class="data-row"><span class="label">实际记录数:</span><span class="value">129024</span></div>
          <div class="data-row"><span class="label">完整率:</span><span class="value">60%</span></div>
        </div>
      </div>
    </body>
    </html>
  `

  // 创建新窗口并打印
  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(printContent)
    printWindow.document.close()

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      printWindow.print()
      printWindow.close()
    }

    ElMessage.success(`正在打印分析结果: ${row.name}`)
  } else {
    ElMessage.error('无法打开打印窗口，请检查浏览器弹窗设置')
  }
}

const handleShare = (row: DataAnalysisItem) => {
  // 创建分享链接
  const shareUrl = `${window.location.origin}/reportIntegrationSpotCheck/dataAnalysis/share/${row.id}`

  // 创建分享内容
  const shareContent = `数据分析结果分享

分析名称: ${row.name}
创建者: ${row.creator}
创建时间: ${row.createTime}
分析状态: ${row.status}
进度: ${row.progress}%

查看详细结果: ${shareUrl}

主要分析结果:
• 数据重复率: 60%
• 数据使用率: 60%
• 数据完整率: 60%
• 发现异常值: -3 (体重字段)
• 缺失值数量: 600条
• 不一致记录: 212条

生成时间: ${new Date().toLocaleString()}`

  // 检查是否支持Web Share API
  if (navigator.share) {
    navigator.share({
      title: `数据分析结果 - ${row.name}`,
      text: shareContent,
      url: shareUrl
    }).then(() => {
      ElMessage.success('分享成功')
    }).catch((error) => {
      console.error('分享失败:', error)
      fallbackShare(shareContent, shareUrl)
    })
  } else {
    fallbackShare(shareContent, shareUrl)
  }
}

// 备用分享方法
const fallbackShare = (content: string, url: string) => {
  // 复制到剪贴板
  if (navigator.clipboard) {
    navigator.clipboard.writeText(content).then(() => {
      ElMessage.success('分享内容已复制到剪贴板')
    }).catch(() => {
      // 如果剪贴板API失败，使用传统方法
      legacyCopyToClipboard(content)
    })
  } else {
    legacyCopyToClipboard(content)
  }
}

// 传统复制方法
const legacyCopyToClipboard = (text: string) => {
  const textArea = document.createElement('textarea')
  textArea.value = text
  textArea.style.position = 'fixed'
  textArea.style.left = '-999999px'
  textArea.style.top = '-999999px'
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()

  try {
    document.execCommand('copy')
    ElMessage.success('分享内容已复制到剪贴板')
  } catch (err) {
    ElMessage.error('复制失败，请手动复制分享链接')
    console.error('复制失败:', err)
  }

  document.body.removeChild(textArea)
}

const handlePause = (row: DataAnalysisItem) => {
  dataAnalysisStorage.update(row.id, { status: DataAnalysisStatus.PAUSED })
  historyStorage.addRecord(row.id, row.name, '暂停', '当前用户')
  ElMessage.success('任务已暂停')
  loadTableData()
}

const handleResume = (row: DataAnalysisItem) => {
  dataAnalysisStorage.update(row.id, { status: DataAnalysisStatus.RUNNING })
  historyStorage.addRecord(row.id, row.name, '恢复', '当前用户')
  ElMessage.success('任务已恢复')
  loadTableData()
}

const handleTerminate = (row: DataAnalysisItem) => {
  dataAnalysisStorage.update(row.id, { status: DataAnalysisStatus.TERMINATED })
  historyStorage.addRecord(row.id, row.name, '终止', '当前用户')
  ElMessage.success('任务已终止')
  loadTableData()
}

const handleStart = (row: DataAnalysisItem) => {
  const startTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
  dataAnalysisStorage.update(row.id, { 
    status: DataAnalysisStatus.RUNNING, 
    startTime,
    progress: 5
  })
  historyStorage.addRecord(row.id, row.name, '启动', '当前用户')
  ElMessage.success('任务已启动')
  loadTableData()
}

const handleEdit = (row: DataAnalysisItem) => {
  currentRow.value = row
  showCreateDialog.value = true
}

const handleDelete = (row: DataAnalysisItem) => {
  dataAnalysisStorage.delete(row.id)
  historyStorage.addRecord(row.id, row.name, '删除', '当前用户')
  ElMessage.success('删除成功')
  loadTableData()
}

const handleRestart = (row: DataAnalysisItem) => {
  const startTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
  dataAnalysisStorage.update(row.id, { 
    status: DataAnalysisStatus.RUNNING, 
    startTime,
    endTime: undefined,
    progress: 0,
    errorMessage: undefined
  })
  historyStorage.addRecord(row.id, row.name, '重新运行', '当前用户')
  ElMessage.success('任务已重新启动')
  loadTableData()
}

const handleViewLog = (row: DataAnalysisItem) => {
  ElMessage.info(`查看日志: ${row.name}`)
}

// 新建数据分析
const onClickCreate = () => {
  currentRow.value = null
  showCreateDialog.value = true
}

// 查看历史记录
const onClickHistory = () => {
  showHistoryDialog.value = true
}

// 算法设置
const onClickAlgorithm = () => {
  showAlgorithmDialog.value = true
}

// 分页事件
const onPaginationChange = (val: number, type: 'page' | 'size') => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1
  }
  loadTableData()
}

// 块高度变化事件
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 75
}

// 返回上一页
const handleGoBack = () => {
  router.push('/reportIntegrationSpotCheck')
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case DataAnalysisStatus.PENDING:
      return 'info'
    case DataAnalysisStatus.RUNNING:
      return 'primary'
    case DataAnalysisStatus.PAUSED:
      return 'warning'
    case DataAnalysisStatus.COMPLETED:
      return 'success'
    case DataAnalysisStatus.FAILED:
      return 'danger'
    case DataAnalysisStatus.TERMINATED:
      return 'info'
    default:
      return 'info'
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadTableData()
})
</script>

<template>
  <div class="data-analysis">
    <Block 
      title="数据分析" 
      :enable-fixed-height="true" 
      :enable-expand-content="true"
      @height-changed="onBlockHeightChanged"
      @content-expand="loadTableData"
    >
      <template #topRight>
        <el-button
          size="small"
          type="default"
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>

        <el-button size="small" type="primary" @click="onClickCreate">
          新建数据分析
        </el-button>
        <el-button size="small" type="info" @click="onClickHistory">
          数据分析历史操作记录
        </el-button>
        <el-button size="small" type="warning" @click="onClickAlgorithm">
          数据分析算法设置
        </el-button>
      </template>
      
      <template #expand>
        <!-- 搜索区域 -->
        <div class="search">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="3"
            :label-width="80"
            :enable-reset="true"
            confirm-text="查询"
            reset-text="重置"
            button-vertical="flowing"
            :loading="searchLoading"
            @submit="onSearch"
            @reset="onReset"
          />
        </div>
      </template>
      
      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        :height="tableHeight"
        stripe
        border
        element-loading-text="加载中..."
        style="width: 100%"
      >
        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :align="column.align || 'left'"
        >
          <template #default="{ row }">
            <!-- 状态列自定义渲染 -->
            <template v-if="column.prop === 'status'">
              <el-tag
                :type="getStatusTagType(row.status)"
                size="small"
              >
                {{ row.status }}
              </el-tag>
            </template>

            <!-- 进度列自定义渲染 -->
            <template v-else-if="column.prop === 'progress'">
              <el-progress
                :percentage="row.progress"
                :stroke-width="6"
                :show-text="true"
                :status="row.status === DataAnalysisStatus.FAILED ? 'exception' : undefined"
              />
            </template>

            <!-- 日志列自定义渲染 -->
            <template v-else-if="column.prop === 'logs'">
              <el-button
                type="info"
                size="small"
                link
                @click="handleViewLog(row)"
              >
                查看日志
              </el-button>
            </template>

            <!-- 普通列 -->
            <template v-else>
              {{ row[column.prop] }}
            </template>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column
          label="操作"
          width="300px"
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            <div class="operation-buttons">
              <template v-for="btn in getButtons(row)" :key="btn.code">
                <el-popconfirm
                  v-if="btn.popconfirm"
                  :title="btn.popconfirm"
                  @confirm="onTableClickButton({ row, btn })"
                >
                  <template #reference>
                    <el-button
                      :type="btn.type"
                      size="small"
                      link
                    >
                      {{ btn.label }}
                    </el-button>
                  </template>
                </el-popconfirm>
                <el-button
                  v-else
                  :type="btn.type"
                  size="small"
                  link
                  @click="onTableClickButton({ row, btn })"
                >
                  {{ btn.label }}
                </el-button>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        :disabled="loading"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      />
    </Block>

    <!-- 新建/编辑数据分析弹窗 -->
    <CreateAnalysisDialog
      v-model:visible="showCreateDialog"
      :edit-data="currentRow"
      @success="loadTableData"
    />

    <!-- 历史操作记录弹窗 -->
    <HistoryDialog
      v-model:visible="showHistoryDialog"
    />

    <!-- 算法设置弹窗 -->
    <AlgorithmDialog
      v-model:visible="showAlgorithmDialog"
    />

    <!-- 分析结果弹窗 -->
    <AnalysisResultDialog
      v-model:visible="showResultDialog"
      :analysis-data="currentAnalysisData"
    />
  </div>
</template>



<route>
{
  meta: {
    title: '数据分析',
  },
}
</route>

<style scoped lang="scss">
.data-analysis {
  .search {
    margin-bottom: 16px;
  }
  
  .operation-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    
    .el-button {
      margin: 0;
    }
  }
}
</style>
