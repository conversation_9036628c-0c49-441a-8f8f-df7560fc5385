<script setup lang="ts" name="taskTransfer">
console.log('TaskTransfer page loaded successfully!')

// 搜索表单配置
const searchFormProp = ref([
  {label: '关键词', prop: 'keyword', type: 'text', placeholder: '请输入任务名称'},
  {
    label: '负责人',
    prop: 'assignee',
    type: 'select',
    placeholder: '请选择负责人',
    options: [
      {label: '张三', value: '张三'},
      {label: '李四', value: '李四'},
      {label: '王五', value: '王五'},
      {label: '马云', value: '马云'},
      {label: '马化腾', value: '马化腾'},
    ]
  },
  {
    label: '转派原因',
    prop: 'transferReason',
    type: 'select',
    placeholder: '请选择转派原因',
    options: [
      {label: '这里是转派原因', value: '这里是转派原因'},
      {label: '未知原因', value: '未知原因'},
      {label: '不可抗力原因', value: '不可抗力原因'},
    ]
  },
  {
    label: '任务状态',
    prop: 'taskStatus',
    type: 'select',
    placeholder: '请选择任务状态',
    options: [
      {label: '待执行', value: '待执行'},
      {label: '提醒中', value: '提醒中'},
      {label: '建议中', value: '建议中'},
      {label: '已到期', value: '已到期'},
      {label: '已完成', value: '已完成'},
    ]
  }
])

const searchForm = ref({
  keyword: '',
  assignee: '',
  transferReason: '',
  taskStatus: ''
})

// 表格列配置
const columns = [
  {prop: 'taskName', label: '任务名称'},
  {prop: 'originalAssignee', label: '原负责人'},
  {prop: 'newAssignee', label: '新负责人'},
  {prop: 'transferReason', label: '转派原因'},
  {prop: 'transferStatus', label: '转派情况'},
  {prop: 'transferTime', label: '转派时间'},
  {prop: 'taskStatus', label: '任务状态'},
]

// 操作按钮配置
const buttons = [
  {label: '权限分发', type: 'primary', code: 'bidirectional'}
]

// 分页配置
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
})

// 模拟数据
const mockData = ref<any[]>([])
const loading = ref(false)
const tableRef = ref()
const tableHeight = ref(500)

// 本地存储key
const STORAGE_KEY = 'taskTransferData'

// 数据管理岗接收设置弹窗
const dataManagementDialogVisible = ref(false)

// 编辑接收分类弹窗
const editCategoryDialogVisible = ref(false)
const editCategoryForm = ref({
  name: '',
  subName: ''
})
const editCategoryIndex = ref(-1)
const editCategoryFormRef = ref()

// 编辑表单验证规则
const editCategoryRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}

// 接收分类数据
const receiveCategories = ref([
  {
    name: '分类名称',
    subName: '',
    isEnabled: true,
    operations: ['添加子分类', '编辑', '删除']
  },
  {
    name: '分类名称',
    subName: '子分类名称',
    isEnabled: true,
    operations: ['编辑', '删除']
  }
])

// 筛选条件设置
const filterConditions = ref({
  taskName: true,
  creator: true,
  createTime: true,
  createDate: true,
  taskStatus: true,
  currentStatus: true,
  reminderPeriod: true
})

// 提醒级别设置
const reminderLevel = ref('高级')
const reminderLevelOptions = [
  { label: '高级', value: '高级' },
  { label: '中级', value: '中级' },
  { label: '低级', value: '低级' }
]

// 提醒动画设置
const reminderAnimation = ref({
  level: 33,
  style: '百叶窗'
})

// 提醒图标设置
const reminderIcon = ref({
  icon: '',
  isCustomColor: false,
  color: '#409EFF'
})

// 生成模拟数据
const generateMockData = () => {
  const taskNames = [
    '数据接送任务', '系统维护任务', '报告编写任务', '会议组织任务', '培训安排任务',
    '项目评估任务', '质量检查任务', '文档整理任务', '客户沟通任务', '技术支持任务',
    '流程优化任务', '安全检查任务', '数据分析任务', '用户调研任务', '产品测试任务'
  ]
  const assignees = ['张三', '李四', '王五', '马云', '马化腾', '赵六', '孙七', '周八']
  const reasons = [
    '这里是转派原因', '未知原因', '不可抗力原因', '人员调整',
    '技能匹配', '工作量平衡', '紧急事务', '专业要求'
  ]
  const transferStatuses = ['0/10', '1/10', '3/10', '5/10', '8/10', '10/10']
  const taskStatuses = ['待执行', '提醒中', '建议中', '已到期', '已完成']
  const statusColors: Record<string, string> = {
    '待执行': 'warning',
    '提醒中': 'warning',
    '建议中': 'primary',
    '已到期': 'danger',
    '已完成': 'success'
  }

  const data = []
  for (let i = 1; i <= 25; i++) {
    const taskStatus = taskStatuses[Math.floor(Math.random() * taskStatuses.length)]
    data.push({
      id: i,
      taskName: taskNames[Math.floor(Math.random() * taskNames.length)],
      originalAssignee: assignees[Math.floor(Math.random() * assignees.length)],
      newAssignee: assignees[Math.floor(Math.random() * assignees.length)],
      transferReason: reasons[Math.floor(Math.random() * reasons.length)],
      transferStatus: transferStatuses[Math.floor(Math.random() * transferStatuses.length)],
      transferTime: `2024-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      taskStatus: taskStatus,
      statusColor: statusColors[taskStatus]
    })
  }
  mockData.value = data
  pagination.total = data.length
}

// 过滤数据
const filteredData = computed(() => {
  let data = [...mockData.value]

  if (searchForm.value.keyword) {
    data = data.filter(item => item.taskName.includes(searchForm.value.keyword))
  }
  if (searchForm.value.assignee) {
    data = data.filter(item =>
      item.originalAssignee === searchForm.value.assignee ||
      item.newAssignee === searchForm.value.assignee
    )
  }
  if (searchForm.value.transferReason) {
    data = data.filter(item => item.transferReason === searchForm.value.transferReason)
  }
  if (searchForm.value.taskStatus) {
    data = data.filter(item => item.taskStatus === searchForm.value.taskStatus)
  }

  pagination.total = data.length
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return data.slice(start, end)
})

// 查询功能
const onSearch = () => {
  pagination.page = 1
}

// 重置功能
const onReset = () => {
  searchForm.value = {
    keyword: '',
    assignee: '',
    transferReason: '',
    taskStatus: ''
  }
  pagination.page = 1
}

// 操作按钮点击
const onTableClickButton = ({row, btn}: any) => {
  if (btn.code === 'bidirectional') {
    ElMessage.success(`对任务"${row.taskName}"执行双向分发操作`)
  }
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type == 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1
  }
}

// 打开数据管理岗接收设置弹窗
const openDataManagementDialog = () => {
  dataManagementDialogVisible.value = true
}

// 关闭数据管理岗接收设置弹窗
const closeDataManagementDialog = () => {
  dataManagementDialogVisible.value = false
}

// 确认数据管理岗接收设置
const confirmDataManagementSettings = () => {
  ElMessage.success('数据管理岗接收设置已保存')
  dataManagementDialogVisible.value = false
}

// 取消数据管理岗接收设置
const cancelDataManagementSettings = () => {
  dataManagementDialogVisible.value = false
}

// 新增接收分类
const addReceiveCategory = () => {
  const newCategory = {
    name: '新分类名称',
    subName: '',
    isEnabled: true,
    operations: ['添加子分类', '编辑', '删除']
  }
  receiveCategories.value.push(newCategory)
  ElMessage.success('已添加新的接收分类')
}

// 打开编辑接收分类对话框
const openEditCategoryDialog = (index: number) => {
  editCategoryIndex.value = index
  const category = receiveCategories.value[index]
  editCategoryForm.value = {
    name: category.name,
    subName: category.subName || ''
  }
  editCategoryDialogVisible.value = true
}

// 关闭编辑接收分类对话框
const closeEditCategoryDialog = () => {
  editCategoryDialogVisible.value = false
  editCategoryForm.value = {
    name: '',
    subName: ''
  }
  editCategoryIndex.value = -1
}

// 确认编辑接收分类
const confirmEditCategory = async () => {
  if (!editCategoryFormRef.value) return

  try {
    await editCategoryFormRef.value.validate()

    const index = editCategoryIndex.value
    if (index >= 0 && index < receiveCategories.value.length) {
      // 更新分类数据
      receiveCategories.value[index] = {
        ...receiveCategories.value[index],
        name: editCategoryForm.value.name,
        subName: editCategoryForm.value.subName
      }

      ElMessage.success('编辑成功')
      closeEditCategoryDialog()
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 处理接收分类操作
const handleCategoryOperation = (operation: string, index: number) => {
  switch (operation) {
    case '添加子分类':
      // 在当前行下方添加子分类
      const subCategory = {
        name: receiveCategories.value[index].name,
        subName: '新子分类名称',
        isEnabled: true,
        operations: ['编辑', '删除']
      }
      receiveCategories.value.splice(index + 1, 0, subCategory)
      ElMessage.success('已添加子分类')
      break
    case '编辑':
      openEditCategoryDialog(index)
      break
    case '删除':
      ElMessageBox.confirm('确认删除此分类吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        receiveCategories.value.splice(index, 1)
        ElMessage.success('删除成功')
      }).catch(() => {
        ElMessage.info('已取消删除')
      })
      break
  }
}

// 初始化
onMounted(() => {
  generateMockData()
})</script>

<template>
  <div class="task-transfer">
    <Block title="任务转派管理" :enable-fixed-height="true">
      <template #topRight>
        <el-button size="small" type="primary" @click="onSearch">查询</el-button>
        <el-button size="small" @click="onReset">重置</el-button>
        <el-button size="small" type="success" @click="openDataManagementDialog">数据管理岗接收设置</el-button>
      </template>

      <template #expand>
        <!-- 搜索表单 -->
        <div class="search">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="4"
            :label-width="80"
            :enable-reset="false"
            :enable-button="false"
          />
        </div>
      </template>

      <!-- 表格 -->
      <TableV2
        ref="tableRef"
        :defaultTableData="filteredData"
        :columns="columns"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="false"
        :enable-index="true"
        :height="tableHeight"
        :buttons="buttons"
        :loading="loading"
        @click-button="onTableClickButton"
      >
        <!-- 自定义任务状态列 -->
        <template #taskStatus="{ row }">
          <el-tag :type="row.statusColor" size="small">
            {{ row.taskStatus }}
          </el-tag>
        </template>
      </TableV2>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      />
    </Block>

    <!-- 数据管理岗接收设置弹窗 -->
    <el-dialog
      v-model="dataManagementDialogVisible"
      title="数据管理岗"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="data-management-content">
        <!-- 接收分类 -->
        <div class="section">
          <div class="section-header">
            <span class="section-title">接收分类</span>
            <el-button type="primary" size="small" @click="addReceiveCategory">新增</el-button>
          </div>

          <el-table :data="receiveCategories" border style="width: 100%; margin-top: 12px;">
            <el-table-column prop="name" label="分类名称" width="150" />
            <el-table-column prop="subName" label="子类名称" width="150" />
            <el-table-column label="是否启用" width="120">
              <template #default="{ row }">
                <el-switch v-model="row.isEnabled" />
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="{ row, $index }">
                <el-button
                  v-for="op in row.operations"
                  :key="op"
                  type="text"
                  size="small"
                  style="margin-right: 8px;"
                  @click="handleCategoryOperation(op, $index)"
                >
                  {{ op }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 筛选条件设置 -->
        <div class="section">
          <h4 class="section-title">筛选条件设置</h4>
          <div class="checkbox-group">
            <el-checkbox v-model="filterConditions.taskName">任务名称</el-checkbox>
            <el-checkbox v-model="filterConditions.creator">创建人</el-checkbox>
            <el-checkbox v-model="filterConditions.createTime">创建时间</el-checkbox>
            <el-checkbox v-model="filterConditions.createDate">创建日期</el-checkbox>
            <el-checkbox v-model="filterConditions.taskStatus">任务状态</el-checkbox>
            <el-checkbox v-model="filterConditions.currentStatus">当前状态</el-checkbox>
            <el-checkbox v-model="filterConditions.reminderPeriod">提醒周期</el-checkbox>
          </div>
        </div>

        <!-- 提醒级别设置 -->
        <div class="section">
          <h4 class="section-title">提醒级别设置</h4>
          <el-select v-model="reminderLevel" placeholder="请选择提醒级别" style="width: 200px;">
            <el-option
              v-for="item in reminderLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>

        <!-- 提醒动画 -->
        <div class="section">
          <h4 class="section-title">提醒动画</h4>
          <div class="animation-setting">
            <span>进入</span>
            <div class="animation-level">
              <span>{{ reminderAnimation.level }}</span>
              <div class="stars">
                <i v-for="n in 6" :key="n" class="star" :class="{ active: n <= Math.floor(reminderAnimation.level / 20) + 1 }">★</i>
              </div>
            </div>
            <div class="animation-types">
              <span>百叶窗</span>
              <span>淡入</span>
              <span>出现</span>
              <span>飞入</span>
              <span>盒状</span>
              <span>棋盘进入</span>
            </div>
          </div>
        </div>

        <!-- 提醒图标 -->
        <div class="section">
          <h4 class="section-title">提醒图标</h4>
          <div class="icon-setting">
            <div class="icon-preview">
              <div class="icon-box">+</div>
            </div>
            <div class="icon-options">
              <el-checkbox v-model="reminderIcon.isCustomColor">是否渐变</el-checkbox>
              <el-color-picker v-model="reminderIcon.color" />
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDataManagementSettings">取消</el-button>
          <el-button type="primary" @click="confirmDataManagementSettings">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑接收分类弹窗 -->
    <el-dialog
      v-model="editCategoryDialogVisible"
      title="编辑接收分类"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="editCategoryFormRef"
        :model="editCategoryForm"
        :rules="editCategoryRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input
            v-model="editCategoryForm.name"
            placeholder="请输入分类名称"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="子类名称">
          <el-input
            v-model="editCategoryForm.subName"
            placeholder="请输入子类名称（可选）"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeEditCategoryDialog">取消</el-button>
          <el-button type="primary" @click="confirmEditCategory">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<route>
{
  meta: {
    title: '任务转派管理',
  },
}
</route>

<style scoped lang="scss">
.task-transfer {
  .search {
    margin-bottom: 16px;

    :deep(.el-form) {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  // 表格样式优化
  :deep(.el-table) {
    .el-table__cell {
      padding: 12px 8px;

      // 确保文本不会溢出
      .cell {
        word-break: break-word;
        line-height: 1.4;
      }
    }

    // 序号列居中
    .el-table__column--selection,
    .el-table__column--index {
      text-align: center;
    }

    // 状态标签样式
    .el-tag {
      border-radius: 4px;
      font-size: 12px;
      padding: 2px 8px;
    }

    // 操作按钮样式
    .el-button {
      padding: 4px 12px;
      font-size: 12px;
    }
  }

  // 分页样式
  :deep(.el-pagination) {
    margin-top: 16px;
    text-align: center;
  }

  // 数据管理岗接收设置弹窗样式
  .data-management-content {
    .section {
      margin-bottom: 24px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      h4.section-title {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }
    }

    // 复选框组样式
    .checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .el-checkbox {
        margin-right: 0;
      }
    }

    // 动画设置样式
    .animation-setting {
      display: flex;
      align-items: center;
      gap: 16px;

      .animation-level {
        display: flex;
        align-items: center;
        gap: 8px;

        .stars {
          display: flex;
          gap: 2px;

          .star {
            color: #ddd;
            font-size: 16px;

            &.active {
              color: #ffd700;
            }
          }
        }
      }

      .animation-types {
        display: flex;
        gap: 12px;

        span {
          padding: 4px 8px;
          background: #f5f7fa;
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;

          &:hover {
            background: #e1f3d8;
          }
        }
      }
    }

    // 图标设置样式
    .icon-setting {
      display: flex;
      align-items: center;
      gap: 16px;

      .icon-preview {
        .icon-box {
          width: 40px;
          height: 40px;
          border: 2px dashed #ddd;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          color: #999;
          cursor: pointer;

          &:hover {
            border-color: #409eff;
            color: #409eff;
          }
        }
      }

      .icon-options {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
  }

  // 弹窗底部按钮样式
  .dialog-footer {
    text-align: right;

    .el-button {
      margin-left: 12px;
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .search {
      :deep(.el-form) {
        .el-form-item {
          width: 50% !important;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .search {
      :deep(.el-form) {
        .el-form-item {
          width: 100% !important;
        }
      }
    }
  }
}
</style>