<!-- 任务关系图谱编辑器主页面 -->
<script setup lang="ts" name="TaskRelationshipGraphEditor">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { useGraphStore } from './stores/useGraphStore'
import { useGraphOperations } from './composables/useGraphOperations'
import GraphCanvas from './components/GraphCanvas.vue'
import GraphToolbar from './components/GraphToolbar.vue'
import ControlPanel from './components/ControlPanel.vue'

import type { ExportFormat } from '@/define/taskGraph.define'
import html2canvas from 'html2canvas'

// ==================== 路由和状态管理 ====================

const route = useRoute()
const router = useRouter()
const graphStore = useGraphStore()
const graphOperations = useGraphOperations()

// ==================== 响应式数据 ====================

/** 任务ID */
const taskId = ref(route.params.id as string)

/** 画布引用 */
const canvasRef = ref()

/** 工具栏组件引用 */
const toolbarRef = ref<InstanceType<typeof GraphToolbar>>()

/** 是否全屏模式 */
const isFullscreen = ref(false)

/** 是否正在加载 */
const loading = ref(true)

/** 是否只读模式 */
const readonly = ref(false)

/** 是否显示悬停信息 */
const showHoverInfo = ref(true)

/** 选择时是否高亮 */
const highlightOnSelect = ref(true)

/** 是否显示节点标签 */
const showNodeLabels = ref(true)

/** 是否显示边标签 */
const showEdgeLabels = ref(true)

/** 布局模式 */
const layoutMode = ref('manual')

// ==================== 生命周期 ====================

onMounted(async () => {
  try {
    // 初始化图谱数据
    await initializeGraph()
    
    // 监听键盘事件
    document.addEventListener('keydown', handleGlobalKeydown)
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleWindowResize)
    
    // 自动保存定时器
    startAutoSave()
    
  } catch (error) {
    ElMessage.error('图谱初始化失败')
    console.error('Graph initialization error:', error)
  } finally {
    loading.value = false
  }
})

onUnmounted(() => {
  // 清理事件监听
  document.removeEventListener('keydown', handleGlobalKeydown)
  window.removeEventListener('resize', handleWindowResize)
  
  // 清理自动保存
  stopAutoSave()
})

// ==================== 自动保存 ====================

let autoSaveTimer: NodeJS.Timeout | null = null

const startAutoSave = () => {
  autoSaveTimer = setInterval(() => {
    if (graphStore.isDirty && !readonly.value) {
      graphStore.saveGraph()
    }
  }, 30000) // 30秒自动保存
}

const stopAutoSave = () => {
  if (autoSaveTimer) {
    clearInterval(autoSaveTimer)
    autoSaveTimer = null
  }
}

// ==================== 初始化方法 ====================

/**
 * 初始化图谱
 */
const initializeGraph = async () => {
  if (!taskId.value) {
    ElMessage.error('任务ID不能为空')
    return
  }
  
  // 尝试从本地存储加载图谱
  const loaded = graphStore.loadGraph(taskId.value)

  if (!loaded) {
    // 如果没有保存的图谱，则基于任务数据初始化
    graphStore.initializeGraph(taskId.value)
  }

  // 等待下一帧后适应视图
  await nextTick()
  if (canvasRef.value) {
    if (loaded && graphStore.viewport) {
      // 如果是加载的数据，恢复保存的视口状态
      canvasRef.value.setViewport(graphStore.viewport)
    } else {
      // 如果是新创建的图谱，适应视图
      canvasRef.value.fitView()
    }
  }
}

// ==================== 事件处理方法 ====================

/**
 * 全局键盘事件处理
 */
const handleGlobalKeydown = (event: KeyboardEvent) => {
  // F11 全屏切换
  if (event.key === 'F11') {
    event.preventDefault()
    toggleFullscreen()
  }
  
  // Ctrl+S 保存
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault()
    handleSave()
  }
}

/**
 * 窗口大小变化处理
 */
const handleWindowResize = () => {
  // 延迟适应视图，避免频繁调用
  setTimeout(() => {
    if (canvasRef.value) {
      canvasRef.value.fitView()
    }
  }, 300)
}

/**
 * 返回上一页
 */
const handleBack = () => {
  router.push(`/taskObjectiveDecomposition/detail/${taskId.value}`)
}

/**
 * 保存图谱
 */
const handleSave = () => {
  const success = graphStore.saveGraph()
  if (success) {
    ElMessage.success('图谱保存成功')
  } else {
    ElMessage.error('图谱保存失败')
  }
}

/**
 * 导出图谱
 */
const handleExport = async (format: ExportFormat) => {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: `正在导出${format.toUpperCase()}格式...`,
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  try {
    switch (format) {
      case 'png':
        await exportToPNG()
        break
      case 'svg':
        await exportToSVG()
        break
      case 'json':
        await exportToJSON()
        break
      default:
        throw new Error(`不支持的导出格式: ${format}`)
    }
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('Export error:', error)
  } finally {
    loadingInstance.close()
  }
}

/**
 * 导出为PNG
 */
const exportToPNG = async () => {
  const canvasElement = document.querySelector('.vue-flow-container')
  if (!canvasElement) throw new Error('找不到画布元素')
  
  const canvas = await html2canvas(canvasElement as HTMLElement, {
    backgroundColor: '#ffffff',
    scale: 2,
    useCORS: true
  })
  
  // 下载图片
  const link = document.createElement('a')
  link.download = `task-graph-${taskId.value}-${Date.now()}.png`
  link.href = canvas.toDataURL()
  link.click()
}

/**
 * 导出为SVG
 */
const exportToSVG = async () => {
  // 这里可以实现SVG导出逻辑
  ElMessage.info('SVG导出功能开发中')
}

/**
 * 导出为JSON
 */
const exportToJSON = async () => {
  const graphData = graphStore.currentGraph
  if (!graphData) throw new Error('没有图谱数据')
  
  const dataStr = JSON.stringify(graphData, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.download = `task-graph-${taskId.value}-${Date.now()}.json`
  link.href = url
  link.click()
  
  URL.revokeObjectURL(url)
}

/**
 * 导入图谱
 */
const handleImport = (file: File) => {
  const reader = new FileReader()
  
  reader.onload = (e) => {
    try {
      const graphData = JSON.parse(e.target?.result as string)
      
      // 验证数据格式
      if (!graphData.nodes || !graphData.edges) {
        throw new Error('无效的图谱数据格式')
      }
      
      // 更新store数据
      graphStore.currentGraph = graphData
      graphStore.nodes = graphData.nodes
      graphStore.edges = graphData.edges
      graphStore.viewport = graphData.viewport || { x: 0, y: 0, zoom: 1 }
      
      ElMessage.success('图谱导入成功')
      
      // 适应视图
      nextTick(() => {
        if (canvasRef.value) {
          canvasRef.value.fitView()
        }
      })
      
    } catch (error) {
      ElMessage.error('图谱导入失败：数据格式错误')
      console.error('Import error:', error)
    }
  }
  
  reader.readAsText(file)
}

/**
 * 刷新图谱
 */
const handleRefresh = () => {
  // 从任务数据重新生成图谱
  graphStore.refreshFromTaskData(taskId.value)

  // 适应视图
  nextTick(() => {
    if (canvasRef.value) {
      canvasRef.value.fitView()
    }
  })
}

/**
 * 缩放控制
 */
const handleZoomIn = () => {
  if (canvasRef.value) {
    const currentZoom = graphStore.viewport.zoom
    canvasRef.value.zoomTo(Math.min(currentZoom * 1.2, 4))
  }
}

const handleZoomOut = () => {
  if (canvasRef.value) {
    const currentZoom = graphStore.viewport.zoom
    canvasRef.value.zoomTo(Math.max(currentZoom / 1.2, 0.1))
  }
}

const handleFitView = () => {
  if (canvasRef.value) {
    canvasRef.value.fitView()
  }
}

const handleZoomTo = (zoom: number) => {
  if (canvasRef.value) {
    canvasRef.value.zoomTo(zoom)
  }
}

/**
 * 处理平移
 */
const handleTranslateTo = (x: number, y: number) => {
  // 更新视口位置
  graphStore.updateViewport({
    x,
    y,
    zoom: graphStore.viewport.zoom
  })

  // Vue Flow的平移通过viewport更新自动处理
  ElMessage.info(`画布已平移到 X:${x}, Y:${y}`)
}

/**
 * 处理旋转
 */
const handleRotateTo = (angle: number) => {
  // 这里可以实现画布旋转功能
  // 由于Vue Flow本身不直接支持旋转，可以通过CSS transform实现
  const canvasElement = document.querySelector('.vue-flow-container')
  if (canvasElement) {
    (canvasElement as HTMLElement).style.transform = `rotate(${angle}deg)`
  }

  ElMessage.info(`画布已旋转到 ${angle}°`)
}

/**
 * 处理全屏比例变化
 */
const handleAspectRatioChange = (ratio: string) => {
  ElMessage.info(`全屏比例已设置为 ${ratio}`)

  // 这里可以根据比例调整画布尺寸
  // 实际实现可能需要根据具体需求调整
}

/**
 * 处理截图
 */
const handleScreenshot = async () => {
  try {
    // 获取画布容器
    const canvasContainer = document.querySelector('.vue-flow-container')
    if (!canvasContainer) {
      ElMessage.error('找不到画布容器')
      return
    }

    // 使用html2canvas进行截图
    const canvas = await html2canvas(canvasContainer as HTMLElement, {
      backgroundColor: '#ffffff',
      scale: 2, // 提高截图质量
      useCORS: true,
      allowTaint: true
    })

    // 转换为数据URL
    const dataUrl = canvas.toDataURL('image/png')

    // 将截图数据传递给工具栏组件
    if (toolbarRef.value) {
      toolbarRef.value.setScreenshotData(dataUrl)
    }

    ElMessage.success('截图完成')
  } catch (error) {
    console.error('截图失败:', error)
    ElMessage.error('截图失败，请重试')
  }
}

/**
 * 处理截图预览
 */
const handleScreenshotPreview = () => {
  // 截图预览逻辑已在工具栏组件中实现
}

/**
 * 切换全屏
 */
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}
</script>

<template>
  <div 
    class="task-relationship-graph-editor"
    :class="{ 'fullscreen': isFullscreen }"
    v-loading="loading"
    element-loading-text="正在加载图谱..."
  >
    <!-- 顶部工具栏 -->
    <GraphToolbar
      ref="toolbarRef"
      :readonly="readonly"
      :current-zoom="graphStore.viewport.zoom"
      :current-translate-x="graphStore.viewport.x"
      :current-translate-y="graphStore.viewport.y"
      :current-rotation="0"
      @back="handleBack"
      @save="handleSave"
      @export="handleExport"
      @import="handleImport"
      @refresh="handleRefresh"
      @zoom-in="handleZoomIn"
      @zoom-out="handleZoomOut"
      @zoom-to="handleZoomTo"
      @translate-to="handleTranslateTo"
      @rotate-to="handleRotateTo"
      @aspect-ratio-change="handleAspectRatioChange"
      @screenshot="handleScreenshot"
      @screenshot-preview="handleScreenshotPreview"
      @fit-view="handleFitView"
      @fullscreen="toggleFullscreen"
    />
    
    <!-- 主要内容区域 -->
    <div class="editor-content">
      <!-- 左侧控制面板 -->
      <ControlPanel
        :readonly="readonly"
        v-model:show-hover-info="showHoverInfo"
        v-model:highlight-on-select="highlightOnSelect"
        v-model:show-node-labels="showNodeLabels"
        v-model:show-edge-labels="showEdgeLabels"
        v-model:layout-mode="layoutMode"
        class="left-panel"
      />

      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <GraphCanvas
          ref="canvasRef"
          :readonly="readonly"
          :show-hover-info="showHoverInfo"
          :highlight-on-select="highlightOnSelect"
          :show-node-labels="showNodeLabels"
          :show-edge-labels="showEdgeLabels"
          :layout-mode="layoutMode"
          height="100%"
        />
      </div>
      

    </div>
  </div>
</template>

<style lang="scss" scoped>
.task-relationship-graph-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f0f2f5;
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
  }
  
  .editor-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    
    .left-panel {
      flex-shrink: 0;
      z-index: 10;
    }
    
    .canvas-container {
      flex: 1;
      position: relative;
      background: #ffffff;
      border-radius: 8px;
      margin: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    
    .right-panel {
      flex-shrink: 0;
      z-index: 10;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .task-relationship-graph-editor {
    .editor-content {
      .left-panel,
      .right-panel {
        position: absolute;
        top: 0;
        bottom: 0;
        z-index: 20;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      }
      
      .left-panel {
        left: 0;
      }
      
      .right-panel {
        right: 0;
      }
      
      .canvas-container {
        margin: 8px 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .task-relationship-graph-editor {
    .editor-content {
      .canvas-container {
        margin: 4px 8px;
      }
    }
  }
}
</style>
