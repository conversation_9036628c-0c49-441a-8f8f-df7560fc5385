<!-- 协作分析弹窗组件 -->
<template>
  <el-dialog
    v-model="visible"
    title="协作分析"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
    draggable
    :close-on-click-modal="false"
  >
    <div class="collaboration-analysis">
      <!-- 统计数据卡片 -->
      <div class="stats-container">
        <div class="stats-card" v-for="(stat, index) in statsData" :key="index">
          <div class="stat-content">
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-value" :style="{ color: stat.color }">{{ stat.value }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// ==================== 类型定义 ====================

interface StatData {
  title: string
  value: string
  color: string
}

// ==================== Props & Emits ====================

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

/** 弹窗显示状态 */
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

/** 统计数据 */
const statsData = ref<StatData[]>([
  {
    title: '平均任务完成时间',
    value: '4.2天',
    color: '#1890ff'
  },
  {
    title: '按时完成率',
    value: '87%',
    color: '#52c41a'
  },
  {
    title: '平均协作人数',
    value: '3.5人',
    color: '#fa8c16'
  }
])

// ==================== 事件处理方法 ====================

/**
 * 关闭弹窗
 */
const handleClose = () => {
  visible.value = false
}

/**
 * 取消按钮点击
 */
const handleCancel = () => {
  visible.value = false
}

/**
 * 确认按钮点击
 */
const handleConfirm = () => {
  // 这里可以添加确认逻辑
  console.log('协作分析确认操作')
  visible.value = false
}

// ==================== 监听器 ====================

/** 监听弹窗显示状态 */
watch(visible, (newVisible) => {
  if (newVisible) {
    console.log('协作分析弹窗已打开')
    // 这里可以添加弹窗打开时的逻辑，比如刷新数据
  }
})
</script>

<style lang="scss" scoped>
.collaboration-analysis {
  .stats-container {
    display: flex;
    gap: 20px;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .stats-card {
      flex: 1;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 24px 16px;
      text-align: center;
      transition: all 0.3s ease;
      border: 1px solid #e8e8e8;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      .stat-content {
        .stat-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 12px;
          font-weight: 500;
        }
        
        .stat-value {
          font-size: 28px;
          font-weight: bold;
          line-height: 1;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 响应式布局
@media (max-width: 768px) {
  .collaboration-analysis {
    .stats-container {
      flex-direction: column;
      gap: 16px;
      
      .stats-card {
        .stat-content {
          .stat-value {
            font-size: 24px;
          }
        }
      }
    }
  }
}
</style>
