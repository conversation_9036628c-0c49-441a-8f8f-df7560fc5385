<!--
  图谱工具栏组件

  根据原型图要求重新实现，包含5个功能区域：
  1. 缩放控制：缩小、1x、放大按钮和缩放比例显示
  2. 平移控制：X轴、Y轴数值显示和输入
  3. 旋转控制：角度滑块和数值显示
  4. 全屏控制：比例选择和全屏按钮
  5. 载图功能：载图上传和预览功能
-->
<script setup lang="ts" name="GraphToolbar">
import { ref, computed, watch, onUnmounted } from 'vue'
import {
  ZoomIn,
  ZoomOut,
  FullScreen,
  DocumentAdd,
  Download
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useGraphStore } from '../stores/useGraphStore'
// import { useGraphOperations } from '../composables/useGraphOperations' // 暂时注释
import type { ExportFormat } from '@/define/taskGraph.define'

interface Props {
  /** 是否只读模式 */
  readonly?: boolean
  /** 是否显示返回按钮 */
  showBackButton?: boolean
  /** 当前缩放比例（0.1 到 4.0） */
  currentZoom?: number
  /** 当前X轴平移距离 */
  currentTranslateX?: number
  /** 当前Y轴平移距离 */
  currentTranslateY?: number
  /** 当前旋转角度 */
  currentRotation?: number
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  showBackButton: true,
  currentZoom: 1.0,
  currentTranslateX: 0,
  currentTranslateY: 0,
  currentRotation: 0
})

// 全屏比例选项类型
type AspectRatio = '16:9' | '16:10' | '4:3'

const emit = defineEmits<{
  back: []
  save: []
  export: [format: ExportFormat]
  import: [file: File]
  refresh: []
  zoomIn: []
  zoomOut: []
  fitView: []
  fullscreen: []
  zoomTo: [zoom: number]
  translateTo: [x: number, y: number]
  rotateTo: [angle: number]
  aspectRatioChange: [ratio: AspectRatio]
  screenshot: []
  screenshotPreview: []
}>()

// ==================== 状态管理 ====================

const graphStore = useGraphStore()
// const graphOperations = useGraphOperations() // 暂时注释

/** 当前缩放比例（百分比） */
const zoomPercentage = ref(100)

/** X轴平移距离 */
const translateX = ref(0)

/** Y轴平移距离 */
const translateY = ref(0)

/** 旋转角度 */
const rotationAngle = ref(0)

/** 当前选择的全屏比例 */
const selectedAspectRatio = ref<AspectRatio>('16:9')

/** 截图数据 */
const screenshotData = ref<string | null>(null)

/** 是否显示截图预览 */
const showScreenshotPreview = ref(false)

/** 是否显示导出对话框 */
const showExportDialog = ref(false)

/** 导出记录搜索关键词 */
const exportSearchKeyword = ref('')

/** 导出记录当前页码 */
const exportCurrentPage = ref(1)

/** 导出记录数据 */
const exportRecords = ref([
  {
    id: '01',
    fileName: '航计业务1',
    exporter: '张三丰',
    exportDepartment: '办公室',
    exportTime: '2024-01-01 12:00:01',
    status: '导出中'
  },
  {
    id: '02',
    fileName: '航计业务1',
    exporter: '张三丰',
    exportDepartment: '办公室',
    exportTime: '2024-01-01 12:00:01',
    status: '导出中'
  },
  {
    id: '03',
    fileName: '航计业务1',
    exporter: '张三丰',
    exportDepartment: '办公室',
    exportTime: '2024-01-01 12:00:01',
    status: '已完成'
  },
  {
    id: '04',
    fileName: '航计业务1',
    exporter: '张三丰',
    exportDepartment: '办公室',
    exportTime: '2024-01-01 12:00:01',
    status: '已完成'
  },
  {
    id: '05',
    fileName: '航计业务1',
    exporter: '张三丰',
    exportDepartment: '办公室',
    exportTime: '2024-01-01 12:00:01',
    status: '已完成'
  },
  {
    id: '06',
    fileName: '航计业务1',
    exporter: '张三丰',
    exportDepartment: '办公室',
    exportTime: '2024-01-01 12:00:01',
    status: '已完成'
  },
  {
    id: '07',
    fileName: '航计业务1',
    exporter: '张三丰',
    exportDepartment: '办公室',
    exportTime: '2024-01-01 12:00:01',
    status: '已完成'
  }
])

// 监听外部传入的缩放比例变化
watch(() => props.currentZoom, (newZoom) => {
  if (newZoom) {
    zoomPercentage.value = Math.round(newZoom * 100)
  }
}, { immediate: true })

// 监听外部传入的平移距离变化
watch(() => props.currentTranslateX, (newX) => {
  if (newX !== undefined) {
    translateX.value = Math.round(newX)
  }
}, { immediate: true })

watch(() => props.currentTranslateY, (newY) => {
  if (newY !== undefined) {
    translateY.value = Math.round(newY)
  }
}, { immediate: true })

// 监听外部传入的旋转角度变化
watch(() => props.currentRotation, (newRotation) => {
  if (newRotation !== undefined) {
    rotationAngle.value = Math.round(newRotation)
  }
}, { immediate: true })

// ==================== 常量配置 ====================

/** 全屏比例选项 */
const aspectRatioOptions = [
  { label: '16:9', value: '16:9' as AspectRatio },
  { label: '16:10', value: '16:10' as AspectRatio },
  { label: '4:3', value: '4:3' as AspectRatio }
]

/** 导出格式选项 */
const exportFormats = [
  { label: 'PNG图片', value: 'png' as ExportFormat, icon: 'picture' },
  { label: 'SVG矢量图', value: 'svg' as ExportFormat, icon: 'picture' },
  { label: 'JSON数据', value: 'json' as ExportFormat, icon: 'document' }
]

/** 旋转角度范围 */
const ROTATION_MIN = 0
const ROTATION_MAX = 360

/** 平移距离范围 */
const TRANSLATE_MIN = -1000
const TRANSLATE_MAX = 1000

/** 文件上传引用 */
const fileUploadRef = ref()

/** 是否正在保存 */
const isSaving = ref(false)

/** 是否正在导出 */
const isExporting = ref(false)

/** 是否正在上传图片 */
const isUploading = ref(false)

// 防抖定时器
let translateDebounceTimer: NodeJS.Timeout | null = null
let rotationDebounceTimer: NodeJS.Timeout | null = null

// ==================== 计算属性 ====================

/** 是否有未保存的更改 */
const hasUnsavedChanges = computed(() => graphStore.isDirty)

/** 是否可以撤销 */
const canUndo = computed(() => graphStore.canUndo)

/** 是否可以重做 */
const canRedo = computed(() => graphStore.canRedo)

/** 选中元素数量 */
const selectionCount = computed(() => graphStore.selectedElements.length)

/** 图谱统计信息 */
const graphStats = computed(() => graphStore.graphStats)

/** 过滤后的导出记录 */
const filteredExportRecords = computed(() => {
  if (!exportSearchKeyword.value) {
    return exportRecords.value
  }
  return exportRecords.value.filter(record =>
    record.fileName.toLowerCase().includes(exportSearchKeyword.value.toLowerCase())
  )
})

// 组件卸载时清理URL对象
onUnmounted(() => {
  if (screenshotData.value) {
    // 清理可能的blob URL
    if (screenshotData.value.startsWith('blob:')) {
      URL.revokeObjectURL(screenshotData.value)
    }
  }
})

// ==================== 事件处理方法 ====================

/**
 * 返回上一页
 */
const handleBack = () => {
  if (hasUnsavedChanges.value) {
    ElMessageBox.confirm(
      '当前有未保存的更改，确定要离开吗？',
      '确认离开',
      {
        confirmButtonText: '离开',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      emit('back')
    }).catch(() => {
      // 用户取消
    })
  } else {
    emit('back')
  }
}

/**
 * 保存图谱
 */
const handleSave = async () => {
  if (props.readonly) return
  
  isSaving.value = true
  
  try {
    const success = graphStore.saveGraph()
    if (success) {
      ElMessage.success('图谱保存成功')
      emit('save')
    } else {
      ElMessage.error('图谱保存失败')
    }
  } catch (error) {
    ElMessage.error('保存过程中发生错误')
  } finally {
    isSaving.value = false
  }
}

/**
 * 导出图谱
 */
const handleExport = async (format: ExportFormat) => {
  isExporting.value = true
  
  try {
    emit('export', format)
    ElMessage.success(`开始导出${format.toUpperCase()}格式`)
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    isExporting.value = false
  }
}

/**
 * 导入图谱
 */
const handleImport = () => {
  if (props.readonly) return
  
  if (hasUnsavedChanges.value) {
    ElMessageBox.confirm(
      '导入新图谱将覆盖当前内容，确定继续吗？',
      '确认导入',
      {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      fileUploadRef.value?.click()
    }).catch(() => {
      // 用户取消
    })
  } else {
    fileUploadRef.value?.click()
  }
}

/**
 * 处理文件选择
 */
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    if (file.type !== 'application/json') {
      ElMessage.error('请选择JSON格式的文件')
      return
    }
    
    emit('import', file)
    // 清空文件输入
    target.value = ''
  }
}

/**
 * 刷新图谱
 */
const handleRefresh = () => {
  if (hasUnsavedChanges.value) {
    ElMessageBox.confirm(
      '刷新将丢失未保存的更改，确定继续吗？',
      '确认刷新',
      {
        confirmButtonText: '刷新',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      emit('refresh')
      ElMessage.success('图谱已刷新')
    }).catch(() => {
      // 用户取消
    })
  } else {
    emit('refresh')
    ElMessage.success('图谱已刷新')
  }
}

/**
 * 撤销操作
 */
const handleUndo = () => {
  if (canUndo.value) {
    graphStore.undo()
    ElMessage.success('已撤销')
  }
}

/**
 * 重做操作
 */
const handleRedo = () => {
  if (canRedo.value) {
    graphStore.redo()
    ElMessage.success('已重做')
  }
}

/**
 * 删除选中元素
 */
const handleDeleteSelection = () => {
  if (selectionCount.value > 0) {
    // graphOperations.deleteSelection() // 暂时注释
    ElMessage.info('删除功能开发中')
  }
}

/**
 * 复制选中元素
 */
const handleCopySelection = () => {
  if (selectionCount.value > 0) {
    // 这里可以实现复制功能
    ElMessage.info('复制功能开发中')
  }
}

/**
 * 编辑选中元素
 */
const handleEditSelection = () => {
  const selectedNodes = graphStore.selectedNodes
  const selectedEdges = graphStore.selectedEdges

  if (selectedNodes.length === 1) {
    // graphOperations.editNode(selectedNodes[0].id) // 暂时注释
    ElMessage.info('编辑功能开发中')
  } else if (selectedEdges.length === 1) {
    // graphOperations.editEdge(selectedEdges[0].id) // 暂时注释
    ElMessage.info('编辑功能开发中')
  } else {
    ElMessage.warning('请选择单个元素进行编辑')
  }
}

/**
 * 快速导出
 */
const handleQuickExport = () => {
  handleExport('png' as ExportFormat)
}

/**
 * 处理缩放变化
 */
const handleZoomChange = (value: number | number[]) => {
  const zoomValue = Array.isArray(value) ? value[0] : value
  zoomPercentage.value = zoomValue

  // 将百分比转换为实际缩放比例（0.1 到 4.0）
  const actualZoom = zoomValue / 100

  // 发送缩放事件给父组件
  emit('zoomTo', actualZoom)
}

/**
 * 处理X轴平移变化（带防抖）
 */
const handleTranslateXChange = (value: number | undefined) => {
  if (value !== undefined) {
    translateX.value = value

    // 清除之前的定时器
    if (translateDebounceTimer) {
      clearTimeout(translateDebounceTimer)
    }

    // 设置防抖
    translateDebounceTimer = setTimeout(() => {
      emit('translateTo', value, translateY.value)
    }, 300)
  }
}

/**
 * 处理Y轴平移变化（带防抖）
 */
const handleTranslateYChange = (value: number | undefined) => {
  if (value !== undefined) {
    translateY.value = value

    // 清除之前的定时器
    if (translateDebounceTimer) {
      clearTimeout(translateDebounceTimer)
    }

    // 设置防抖
    translateDebounceTimer = setTimeout(() => {
      emit('translateTo', translateX.value, value)
    }, 300)
  }
}

/**
 * 处理旋转角度变化（带防抖）
 */
const handleRotationChange = (value: number | number[]) => {
  const rotationValue = Array.isArray(value) ? value[0] : value
  rotationAngle.value = rotationValue

  // 清除之前的定时器
  if (rotationDebounceTimer) {
    clearTimeout(rotationDebounceTimer)
  }

  // 设置防抖
  rotationDebounceTimer = setTimeout(() => {
    emit('rotateTo', rotationValue)
  }, 200)
}

/**
 * 处理全屏比例变化
 */
const handleAspectRatioChange = (value: AspectRatio) => {
  selectedAspectRatio.value = value
  emit('aspectRatioChange', value)
}

/**
 * 处理截图
 */
const handleScreenshot = () => {
  isUploading.value = true

  try {
    // 发送截图事件给父组件
    emit('screenshot')
    ElMessage.success('截图成功')
  } catch (error) {
    ElMessage.error('截图失败，请重试')
  } finally {
    isUploading.value = false
  }
}

/**
 * 处理截图预览
 */
const handleScreenshotPreview = () => {
  if (screenshotData.value) {
    showScreenshotPreview.value = true
    emit('screenshotPreview')
  } else {
    ElMessage.warning('请先进行截图')
  }
}

/**
 * 设置截图数据
 */
const setScreenshotData = (dataUrl: string) => {
  screenshotData.value = dataUrl
}

/**
 * 打开导出对话框
 */
const handleExportDialog = () => {
  try {
    showExportDialog.value = true
  } catch (error) {
    ElMessage.error('打开导出对话框失败')
    console.error('导出对话框打开错误:', error)
  }
}

/**
 * 关闭导出对话框
 */
const handleCloseExportDialog = () => {
  showExportDialog.value = false
  exportSearchKeyword.value = ''
}

/**
 * 处理导出记录搜索
 */
const handleExportSearch = () => {
  try {
    // 这里可以实现搜索逻辑
    console.log('搜索关键词:', exportSearchKeyword.value)
    // 重置到第一页
    exportCurrentPage.value = 1
  } catch (error) {
    ElMessage.error('搜索失败，请重试')
    console.error('导出记录搜索错误:', error)
  }
}

/**
 * 重置搜索条件
 */
const handleResetExportSearch = () => {
  exportSearchKeyword.value = ''
  exportCurrentPage.value = 1
}

/**
 * 处理导出记录下载
 */
const handleDownloadExport = (record: any) => {
  try {
    if (record.status === '已完成') {
      ElMessage.success(`开始下载: ${record.fileName}`)
      // 这里可以实现实际的下载逻辑
    } else {
      ElMessage.warning('文件还未导出完成，请稍后再试')
    }
  } catch (error) {
    ElMessage.error('下载失败，请重试')
    console.error('导出记录下载错误:', error)
  }
}

/**
 * 下载截图
 */
const downloadScreenshot = () => {
  if (!screenshotData.value) {
    ElMessage.warning('没有可下载的截图')
    return
  }

  try {
    const link = document.createElement('a')
    link.download = `graph-screenshot-${new Date().getTime()}.png`
    link.href = screenshotData.value
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('截图下载成功')
  } catch (error) {
    ElMessage.error('截图下载失败')
  }
}

// 暴露方法给父组件
defineExpose({
  setScreenshotData
})
</script>

<template>
  <div class="graph-toolbar">
    <!-- 区域1: 缩放控制 -->
    <div class="toolbar-section zoom-section">
      <el-tooltip content="缩小" placement="bottom">
        <el-button
          :icon="ZoomOut"
          @click="$emit('zoomOut')"
          size="small"
          circle
        />
      </el-tooltip>

      <el-tooltip content="适应视图" placement="bottom">
        <el-button
          @click="$emit('fitView')"
          size="small"
          class="zoom-fit-btn"
        >
          1x
        </el-button>
      </el-tooltip>

      <el-tooltip content="放大" placement="bottom">
        <el-button
          :icon="ZoomIn"
          @click="$emit('zoomIn')"
          size="small"
          circle
        />
      </el-tooltip>

      <span class="zoom-value">{{ zoomPercentage }}%</span>
    </div>

    <!-- 区域2: 平移控制 -->
    <div class="toolbar-section translate-section">
      <div class="translate-control">
        <span class="control-label">X轴</span>
        <el-input-number
          v-model="translateX"
          :min="TRANSLATE_MIN"
          :max="TRANSLATE_MAX"
          :step="1"
          size="small"
          controls-position="right"
          @change="handleTranslateXChange"
        />
      </div>

      <div class="translate-control">
        <span class="control-label">Y轴</span>
        <el-input-number
          v-model="translateY"
          :min="TRANSLATE_MIN"
          :max="TRANSLATE_MAX"
          :step="1"
          size="small"
          controls-position="right"
          @change="handleTranslateYChange"
        />
      </div>
    </div>

    <!-- 区域3: 旋转控制 -->
    <div class="toolbar-section rotation-section">
      <span class="control-label">旋转:</span>
      <el-slider
        v-model="rotationAngle"
        :min="ROTATION_MIN"
        :max="ROTATION_MAX"
        :step="1"
        class="rotation-slider"
        @change="handleRotationChange"
      />
      <span class="rotation-value">{{ rotationAngle }}°</span>
    </div>

    <!-- 区域4: 全屏控制 -->
    <div class="toolbar-section fullscreen-section">
      <el-select
        v-model="selectedAspectRatio"
        size="small"
        class="aspect-ratio-select"
        @change="handleAspectRatioChange"
      >
        <el-option
          v-for="option in aspectRatioOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>

      <el-tooltip content="全屏" placement="bottom">
        <el-button
          :icon="FullScreen"
          @click="$emit('fullscreen')"
          size="small"
          circle
        />
      </el-tooltip>
    </div>

    <!-- 区域5: 截图功能 -->
    <div class="toolbar-section screenshot-section">
      <el-tooltip content="截图" placement="bottom">
        <el-button
          size="small"
          type="primary"
          class="screenshot-btn"
          :loading="isUploading"
          @click="handleScreenshot"
        >
          截图
        </el-button>
      </el-tooltip>

      <el-tooltip content="截图预览" placement="bottom">
        <el-button
          size="small"
          :disabled="!screenshotData"
          @click="handleScreenshotPreview"
          class="preview-btn"
        >
          截图预览
        </el-button>
      </el-tooltip>
    </div>

    <!-- 区域6: 操作控制 -->
    <div class="toolbar-section action-section">
      <el-tooltip content="保存" placement="bottom">
        <el-button
          :icon="DocumentAdd"
          size="small"
          type="primary"
          class="save-btn"
          :loading="isSaving"
          @click="handleSave"
        >
          保存
        </el-button>
      </el-tooltip>

      <el-tooltip content="导出" placement="bottom">
        <el-button
          :icon="Download"
          size="small"
          class="export-btn"
          @click="handleExportDialog"
        >
          导出
        </el-button>
      </el-tooltip>
    </div>

    <!-- 截图预览对话框 -->
    <el-dialog
      v-model="showScreenshotPreview"
      title="截图预览"
      width="60%"
      center
    >
      <div class="screenshot-preview-container" v-if="screenshotData">
        <el-image
          :src="screenshotData"
          fit="contain"
          style="width: 100%; max-height: 500px;"
        />
        <div class="preview-actions" style="margin-top: 16px; text-align: center;">
          <el-button type="primary" @click="downloadScreenshot">
            下载截图
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 导出对话框 -->
    <el-dialog
      v-model="showExportDialog"
      title="导出记录"
      width="80%"
      center
      @close="handleCloseExportDialog"
    >
      <div class="export-dialog-content">
        <!-- 搜索区域 -->
        <div class="export-search-area">
          <el-input
            v-model="exportSearchKeyword"
            placeholder="请输入文件名"
            class="search-input"
            clearable
          >
            <template #append>
              <el-button @click="handleExportSearch">查询</el-button>
            </template>
          </el-input>
          <el-button type="primary" style="margin-left: 8px;" @click="handleResetExportSearch">重置</el-button>
        </div>

        <!-- 导出记录表格 -->
        <div class="export-table-area">
          <el-table :data="filteredExportRecords" style="width: 100%">
            <el-table-column prop="id" label="序号" width="80" />
            <el-table-column prop="fileName" label="文件名称" />
            <el-table-column prop="exporter" label="导出人" width="120" />
            <el-table-column prop="exportDepartment" label="导出人所在部门" width="150" />
            <el-table-column prop="exportTime" label="导出时间" width="180" />
            <el-table-column prop="status" label="导出状态" width="120">
              <template #default="scope">
                <el-tag
                  :type="scope.row.status === '已完成' ? 'success' : 'warning'"
                >
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleDownloadExport(scope.row)"
                  :disabled="scope.row.status !== '已完成'"
                >
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="export-pagination-area">
          <el-pagination
            v-model:current-page="exportCurrentPage"
            :page-size="10"
            :total="filteredExportRecords.length"
            layout="total, prev, pager, next, jumper"
            style="justify-content: center;"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 隐藏的文件上传输入（保持向后兼容） -->
    <input
      ref="fileUploadRef"
      type="file"
      accept=".json"
      style="display: none"
      @change="handleFileChange"
    />
  </div>
</template>

<style lang="scss" scoped>
.graph-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  gap: 16px;

  .toolbar-section {
    display: flex;
    align-items: center;
    gap: 8px;

    // 区域1: 缩放控制
    &.zoom-section {
      flex: 0 0 auto;

      .zoom-fit-btn {
        min-width: 40px;
      }

      .zoom-value {
        font-size: 12px;
        color: #666;
        margin-left: 8px;
        min-width: 35px;
      }
    }

    // 区域2: 平移控制
    &.translate-section {
      flex: 0 0 auto;
      gap: 12px;

      .translate-control {
        display: flex;
        align-items: center;
        gap: 4px;

        .control-label {
          font-size: 12px;
          color: #666;
          min-width: 20px;
        }

        :deep(.el-input-number) {
          width: 80px;

          .el-input__inner {
            text-align: center;
          }
        }
      }
    }

    // 区域3: 旋转控制
    &.rotation-section {
      flex: 1 1 auto;
      justify-content: center;
      max-width: 200px;

      .control-label {
        font-size: 12px;
        color: #666;
      }

      .rotation-slider {
        width: 120px;
        margin: 0 8px;
      }

      .rotation-value {
        font-size: 12px;
        color: #666;
        min-width: 30px;
      }
    }

    // 区域4: 全屏控制
    &.fullscreen-section {
      flex: 0 0 auto;

      .aspect-ratio-select {
        width: 70px;
        margin-right: 8px;
      }
    }

    // 区域5: 截图功能
    &.screenshot-section {
      flex: 0 0 auto;

      .screenshot-btn {
        margin-right: 8px;
        min-width: 50px;
      }

      .preview-btn {
        min-width: 70px;
      }
    }

    // 区域6: 操作控制
    &.action-section {
      flex: 0 0 auto;

      .save-btn {
        margin-right: 8px;
        min-width: 60px;
      }

      .export-btn {
        min-width: 60px;
      }
    }
  }
}

// 图片预览对话框样式
.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

// 导出对话框样式
.export-dialog-content {
  .export-search-area {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .search-input {
      flex: 1;
      max-width: 300px;
    }
  }

  .export-table-area {
    margin-bottom: 16px;

    :deep(.el-table) {
      .el-table__header {
        background-color: #f5f7fa;
      }
    }
  }

  .export-pagination-area {
    display: flex;
    justify-content: center;
    margin-top: 16px;
  }
}

// 导出对话框响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .export-dialog-content {
    .export-search-area {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;

      .search-input {
        max-width: none;
      }
    }

    .export-table-area {
      :deep(.el-table) {
        font-size: 12px;

        .el-table__cell {
          padding: 8px 4px;
        }
      }
    }

    .export-pagination-area {
      :deep(.el-pagination) {
        .el-pagination__sizes,
        .el-pagination__jump {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .export-dialog-content {
    .export-table-area {
      :deep(.el-table) {
        .el-table__cell {
          padding: 6px 2px;
          font-size: 11px;
        }

        // 在小屏幕上隐藏部分列
        .el-table__column--selection,
        .el-table__column:nth-child(4), // 导出人所在部门
        .el-table__column:nth-child(5)  // 导出时间
        {
          display: none;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .graph-toolbar {
    gap: 12px;

    .toolbar-section {
      &.rotation-section {
        max-width: 160px;

        .rotation-slider {
          width: 100px;
        }
      }

      &.translate-section {
        gap: 8px;

        .translate-control {
          :deep(.el-input-number) {
            width: 70px;
          }
        }
      }
    }
  }
}

@media (max-width: 992px) {
  .graph-toolbar {
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px 12px;

    .toolbar-section {
      &.zoom-section,
      &.fullscreen-section,
      &.screenshot-section,
      &.action-section {
        order: 1;
      }

      &.translate-section {
        order: 2;
        gap: 6px;

        .translate-control {
          :deep(.el-input-number) {
            width: 65px;
          }
        }
      }

      &.rotation-section {
        order: 3;
        flex-basis: 100%;
        justify-content: center;
        margin-top: 8px;
        max-width: none;

        .rotation-slider {
          width: 150px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .graph-toolbar {
    padding: 6px 8px;

    .toolbar-section {
      &.zoom-section {
        .zoom-value {
          display: none;
        }
      }

      &.translate-section {
        .translate-control {
          .control-label {
            font-size: 11px;
          }

          :deep(.el-input-number) {
            width: 60px;
          }
        }
      }

      &.fullscreen-section {
        .aspect-ratio-select {
          width: 60px;
        }
      }

      &.screenshot-section {
        .screenshot-btn,
        .preview-btn {
          min-width: 40px;
          font-size: 12px;
        }
      }

      &.action-section {
        .save-btn,
        .export-btn {
          min-width: 40px;
          font-size: 12px;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .graph-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;

    .toolbar-section {
      justify-content: center;

      &.rotation-section {
        order: 1;
        margin-top: 0;

        .rotation-slider {
          width: 200px;
        }
      }

      &.translate-section {
        order: 2;
        justify-content: space-around;
      }

      &.zoom-section,
      &.fullscreen-section,
      &.screenshot-section,
      &.action-section {
        order: 3;
        justify-content: space-around;
      }
    }
  }
}
</style>
