<!-- 添加任务关系弹窗组件 -->
<script setup lang="ts" name="AddTaskRelationDialog">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'
import { useGraphStore } from '../stores/useGraphStore'
import { useGraphOperations } from '../composables/useGraphOperations'
import { EdgeType, type EdgeData } from '@/define/taskGraph.define'

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
  confirm: [relationData: any]
}>()

// Store和操作
const graphStore = useGraphStore()
const graphOperations = useGraphOperations()

// 响应式状态
const loading = ref(false)

// 表单数据
const formData = ref({
  sourceNodeId: '',
  targetNodeId: '',
  relationType: EdgeType.DEPENDENCY,
  relationLabel: '',
  lineColor: '#1890ff'
})

// 预定义颜色
const predefineColors = [
  '#1890ff', // 蓝色 - 依赖关系
  '#52c41a', // 绿色 - 顺序关系  
  '#fa8c16', // 橙色 - 并行关系
  '#722ed1', // 紫色 - 条件关系
  '#ff4d4f', // 红色
  '#13c2c2', // 青色
  '#eb2f96', // 粉色
  '#faad14', // 黄色
  '#2f54eb', // 深蓝
  '#8c8c8c'  // 灰色 - 自定义关系
]

// 关系类型选项
const relationTypeOptions = [
  { label: '依赖关系', value: EdgeType.DEPENDENCY, description: '实线箭头，表示任务间的依赖关系' },
  { label: '顺序关系', value: EdgeType.SEQUENCE, description: '实线箭头，带动画，表示任务的执行顺序' },
  { label: '并行关系', value: EdgeType.PARALLEL, description: '虚线，表示任务可以并行执行' },
  { label: '条件关系', value: EdgeType.CONDITIONAL, description: '虚线箭头，表示有条件的任务关系' },
  { label: '自定义关系', value: EdgeType.CUSTOM, description: '自定义的任务关系类型' }
]

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 可选择的节点列表（排除已选中的源节点）
const availableNodes = computed(() => {
  return graphStore.nodes.map(node => ({
    label: node.data.label,
    value: node.id,
    disabled: false
  }))
})

// 可选择的目标节点（排除源节点）
const availableTargetNodes = computed(() => {
  return graphStore.nodes
    .filter(node => node.id !== formData.value.sourceNodeId)
    .map(node => ({
      label: node.data.label,
      value: node.id,
      disabled: false
    }))
})

// 表单验证规则
const formRules = {
  sourceNodeId: [
    { required: true, message: '请选择源节点', trigger: 'change' }
  ],
  targetNodeId: [
    { required: true, message: '请选择目标节点', trigger: 'change' }
  ],
  relationType: [
    { required: true, message: '请选择关系类型', trigger: 'change' }
  ],
  relationLabel: [
    { required: true, message: '请输入关系标签', trigger: 'blur' },
    { min: 1, max: 50, message: '关系标签长度应在1-50个字符之间', trigger: 'blur' }
  ]
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// 监听关系类型变化，自动设置对应的线条颜色
watch(() => formData.value.relationType, (newType) => {
  const colorMap = {
    [EdgeType.DEPENDENCY]: '#1890ff',
    [EdgeType.SEQUENCE]: '#52c41a',
    [EdgeType.PARALLEL]: '#fa8c16',
    [EdgeType.CONDITIONAL]: '#722ed1',
    [EdgeType.CUSTOM]: '#8c8c8c'
  }
  formData.value.lineColor = colorMap[newType] || '#1890ff'
})

// 重置表单
const resetForm = () => {
  formData.value = {
    sourceNodeId: '',
    targetNodeId: '',
    relationType: EdgeType.DEPENDENCY,
    relationLabel: '',
    lineColor: '#1890ff'
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

// 确认添加关系
const handleConfirm = async () => {
  // 表单验证
  if (!formData.value.sourceNodeId) {
    ElMessage.error('请选择源节点')
    return
  }
  if (!formData.value.targetNodeId) {
    ElMessage.error('请选择目标节点')
    return
  }
  if (!formData.value.relationLabel.trim()) {
    ElMessage.error('请输入关系标签')
    return
  }
  if (formData.value.sourceNodeId === formData.value.targetNodeId) {
    ElMessage.error('源节点和目标节点不能相同')
    return
  }

  loading.value = true
  
  try {
    // 检查是否已存在相同的关系
    const existingEdge = graphStore.edges.find(edge => 
      edge.source === formData.value.sourceNodeId && 
      edge.target === formData.value.targetNodeId
    )
    
    if (existingEdge) {
      ElMessage.warning('该关系已存在，请选择其他节点或编辑现有关系')
      return
    }

    // 获取源节点和目标节点信息
    const sourceNode = graphStore.nodes.find(n => n.id === formData.value.sourceNodeId)
    const targetNode = graphStore.nodes.find(n => n.id === formData.value.targetNodeId)
    
    if (!sourceNode || !targetNode) {
      ElMessage.error('节点信息不存在')
      return
    }

    // 构建边数据
    const edgeData: EdgeData = {
      label: formData.value.relationLabel,
      description: `${sourceNode.data.label} → ${targetNode.data.label}`,
      color: formData.value.lineColor,
      animated: formData.value.relationType === EdgeType.SEQUENCE,
      metadata: {
        createdAt: new Date().toISOString(),
        relationType: formData.value.relationType
      }
    }

    // 添加边到图谱
    const newEdge = graphStore.addEdge({
      type: formData.value.relationType,
      source: formData.value.sourceNodeId,
      target: formData.value.targetNodeId,
      data: edgeData,
      animated: edgeData.animated,
      style: {
        stroke: formData.value.lineColor
      }
    })

    if (newEdge) {
      ElMessage.success('任务关系添加成功')
      emit('confirm', {
        edge: newEdge,
        formData: { ...formData.value }
      })
      handleClose()
    } else {
      ElMessage.error('添加任务关系失败')
    }
  } catch (error) {
    console.error('添加任务关系失败:', error)
    ElMessage.error('添加任务关系失败')
  } finally {
    loading.value = false
  }
}

// 源节点变化时清空目标节点选择
const handleSourceNodeChange = () => {
  formData.value.targetNodeId = ''
}
</script>

<template>
  <DialogComp
    v-model="dialogVisible"
    title="添加任务关系"
    width="500px"
    :loading="loading"
    :close-on-click-modal="false"
    @close="handleClose"
    @click-confirm="handleConfirm"
  >
    <div class="add-relation-content">
      <el-form
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="right"
      >
        <!-- 源节点选择 -->
        <el-form-item label="源节点" prop="sourceNodeId">
          <el-select
            v-model="formData.sourceNodeId"
            placeholder="请选择源节点"
            style="width: 100%"
            @change="handleSourceNodeChange"
          >
            <el-option
              v-for="node in availableNodes"
              :key="node.value"
              :label="node.label"
              :value="node.value"
              :disabled="node.disabled"
            />
          </el-select>
          <div class="form-tip">选择关系的起始节点</div>
        </el-form-item>

        <!-- 目标节点选择 -->
        <el-form-item label="目标节点" prop="targetNodeId">
          <el-select
            v-model="formData.targetNodeId"
            placeholder="请选择目标节点"
            style="width: 100%"
            :disabled="!formData.sourceNodeId"
          >
            <el-option
              v-for="node in availableTargetNodes"
              :key="node.value"
              :label="node.label"
              :value="node.value"
              :disabled="node.disabled"
            />
          </el-select>
          <div class="form-tip">选择关系的目标节点</div>
        </el-form-item>

        <!-- 关系类型选择 -->
        <el-form-item label="关系类型" prop="relationType">
          <el-select
            v-model="formData.relationType"
            placeholder="请选择关系类型"
            style="width: 100%"
          >
            <el-option
              v-for="option in relationTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            >
              <div>
                <div>{{ option.label }}</div>
                <div class="option-description">{{ option.description }}</div>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">选择任务间的关系类型</div>
        </el-form-item>

        <!-- 关系标签 -->
        <el-form-item label="关系标签" prop="relationLabel">
          <el-input
            v-model="formData.relationLabel"
            placeholder="请输入关系标签"
            maxlength="50"
            show-word-limit
            clearable
          />
          <div class="form-tip">为关系添加描述性标签</div>
        </el-form-item>

        <!-- 线条颜色 -->
        <el-form-item label="线条颜色">
          <div class="color-picker-group">
            <el-color-picker
              v-model="formData.lineColor"
              :predefine="predefineColors"
              show-alpha
            />
            <span class="color-value">{{ formData.lineColor }}</span>
          </div>
          <div class="form-tip">选择关系线条的颜色</div>
        </el-form-item>
      </el-form>
    </div>
  </DialogComp>
</template>

<style lang="scss" scoped>
.add-relation-content {
  padding: 16px 0;

  .el-form {
    .el-form-item {
      margin-bottom: 24px;
    }
  }

  .form-tip {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
    line-height: 1.4;
  }

  .option-description {
    font-size: 12px;
    color: #999;
    margin-top: 2px;
  }

  .color-picker-group {
    display: flex;
    align-items: center;
    gap: 12px;

    .color-value {
      font-size: 12px;
      color: #666;
      font-family: monospace;
    }
  }
}
</style>
