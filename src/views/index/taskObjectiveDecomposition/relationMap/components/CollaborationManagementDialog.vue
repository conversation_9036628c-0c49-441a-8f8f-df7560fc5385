<!-- 协作管理弹窗组件 -->
<template>
  <el-dialog
    v-model="visible"
    title="协作管理"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
    draggable
    :close-on-click-modal="false"
  >
    <div class="collaboration-management">
      <!-- 表格内容 -->
      <div class="table-container">
        <el-table
          :data="collaborationData"
          style="width: 100%"
          height="400"
          stripe
        >
          <el-table-column prop="sequence" label="序号" width="80" align="center" />
          <el-table-column prop="username" label="用户" min-width="120" />
          <el-table-column prop="role" label="角色" min-width="100" />
          <el-table-column prop="lastOperationTime" label="最后操作时间" min-width="160" />
          <el-table-column label="账号状态" width="120" align="center">
            <template #default="{ row }">
              <el-switch
                v-model="row.accountStatus"
                :disabled="true"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// ==================== 类型定义 ====================

interface CollaborationUser {
  sequence: string
  username: string
  role: string
  lastOperationTime: string
  accountStatus: boolean
}

// ==================== Props & Emits ====================

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

/** 弹窗显示状态 */
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

/** 协作用户数据 */
const collaborationData = ref<CollaborationUser[]>([
  {
    sequence: '01',
    username: '统计业务表1',
    role: '张三丰',
    lastOperationTime: '2024-01-01 12:00:01',
    accountStatus: true
  },
  {
    sequence: '02',
    username: '统计业务表1',
    role: '张三丰',
    lastOperationTime: '2024-01-01 12:00:01',
    accountStatus: true
  },
  {
    sequence: '03',
    username: '统计业务表1',
    role: '张三丰',
    lastOperationTime: '2024-01-01 12:00:01',
    accountStatus: true
  },
  {
    sequence: '04',
    username: '统计业务表1',
    role: '张三丰',
    lastOperationTime: '2024-01-01 12:00:01',
    accountStatus: true
  },
  {
    sequence: '05',
    username: '统计业务表1',
    role: '张三丰',
    lastOperationTime: '2024-01-01 12:00:01',
    accountStatus: true
  },
  {
    sequence: '06',
    username: '统计业务表1',
    role: '张三丰',
    lastOperationTime: '2024-01-01 12:00:01',
    accountStatus: true
  },
  {
    sequence: '07',
    username: '统计业务表1',
    role: '张三丰',
    lastOperationTime: '2024-01-01 12:00:01',
    accountStatus: true
  }
])

// ==================== 事件处理方法 ====================

/**
 * 关闭弹窗
 */
const handleClose = () => {
  visible.value = false
}

/**
 * 取消按钮点击
 */
const handleCancel = () => {
  visible.value = false
}

/**
 * 确认按钮点击
 */
const handleConfirm = () => {
  // 这里可以添加确认逻辑
  console.log('协作管理确认操作')
  visible.value = false
}

// ==================== 监听器 ====================

/** 监听弹窗显示状态 */
watch(visible, (newVisible) => {
  if (newVisible) {
    console.log('协作管理弹窗已打开')
    // 这里可以添加弹窗打开时的逻辑，比如刷新数据
  }
})
</script>

<style lang="scss" scoped>
.collaboration-management {
  .table-container {
    margin-bottom: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 自定义表格样式
:deep(.el-table) {
  .el-table__header {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 500;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 开关样式优化
:deep(.el-switch) {
  .el-switch__core {
    border-color: transparent;
  }
}
</style>
