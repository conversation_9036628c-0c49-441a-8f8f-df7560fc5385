<!-- 数据集Tab组件 -->
<template>
  <div class="data-set-tab">
    <Block title="数据集管理" :enable-fixed-height="true" :enable-expand-content="true" :default-expand="false" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <div class="top-buttons">
          <el-button size="small" type="primary" @click="onClickAdd">创建数据集数据源</el-button>
          <el-button size="small" type="primary" @click="onClickOperationButtonConfig">操作按钮隐藏配置</el-button>
          <el-button size="small" type="primary" @click="onClickDataSetTypeConfig">数据集类型配置</el-button>
          <el-button size="small" type="primary" @click="onClickDataSetCategory">数据集分类</el-button>
          <el-button size="small" type="primary" @click="onClickBatchImport">数据集导入</el-button>
          <el-button size="small" type="primary" @click="onClickBatchExport">数据集导出</el-button>
          <el-button size="small" type="primary" @click="onClickQualityMonitoringAssessment">质量监控与评估</el-button>
          <el-button size="small" type="primary" @click="onClickFieldTemplate">数据集业务表绑定</el-button>

          <el-dropdown @command="handleMoreCommand">
            <el-button size="small" type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="exceptionHandling">访问权限管理</el-dropdown-item>
                <el-dropdown-item command="qualityReportManagement">备份与恢复</el-dropdown-item>
                <el-dropdown-item command="dataSetIdentification">数据集合规性检查</el-dropdown-item>
                <el-dropdown-item command="integrationStrategy">数据校验</el-dropdown-item>
                <el-dropdown-item command="transformationRule">数据备份与恢复验证</el-dropdown-item>
                <el-dropdown-item command="formatFileConfig">数据安全性验证</el-dropdown-item>
                <el-dropdown-item command="dataSetAccessRule">安全性加固设置</el-dropdown-item>
                <el-dropdown-item command="accessAnalysisMonitor">日志管理</el-dropdown-item>
                <el-dropdown-item command="dataSetHealthMonitor">异常处理</el-dropdown-item>
                <el-dropdown-item command="sourceAnalysis">性能管理与监控</el-dropdown-item>
                <el-dropdown-item command="archiveManagement">权限设计</el-dropdown-item>
                <el-dropdown-item command="">数据归档</el-dropdown-item>
                <el-dropdown-item command="">访问审计</el-dropdown-item>
                <el-dropdown-item command="">日志分析与行为监控</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>

      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <div class="search-row">
            <div class="search-fields">
              <el-form :model="searchForm" inline>
                <el-form-item label="数据集名称" label-width="100px">
                  <el-input v-model="searchForm.name" placeholder="请输入名称" size="small" style="width: 160px" clearable />
                </el-form-item>
                <el-form-item label="数据源名称" label-width="100px">
                  <el-input v-model="searchForm.dataSourceName" placeholder="请输入数据源名称" size="small" style="width: 160px" clearable />
                </el-form-item>
                <el-form-item label="数据源类型" label-width="100px">
                  <el-select v-model="searchForm.dataSourceType" placeholder="请选择数据源类型" size="small" style="width: 160px" clearable>
                    <el-option label="MySql" value="MySql" />
                    <el-option label="Oracle" value="Oracle" />
                    <el-option label="SQL Server" value="SQL Server" />
                    <el-option label="达梦" value="达梦" />
                    <el-option label="Hive" value="Hive" />
                    <el-option label="MangoDB" value="MangoDB" />
                    <el-option label="Huawei GaussDB" value="Huawei GaussDB" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
            <div class="search-buttons">
              <el-button size="small" type="primary" @click="onSearch">查询</el-button>
              <el-button size="small" @click="onReset">重置</el-button>
              <el-button size="small" @click="$router.back()">返回</el-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <div style="width: 100%;">
        <BaseTableComp
          :data="paginatedData"
          :colData="tableColumns"
          :buttons="tableButtons"
          :current-page="pagination.page"
          :page-size="pagination.size"
          :total="pagination.total"
          :height="tableHeight"
          :visible-setting="false"
          @size-change="onSizeChange"
          @current-change="onPageChange"
          @selection-change="onSelectionChange"
          @clickButton="onTableButtonClick"
        />
      </div>
    </Block>

    <!-- 新增/编辑/详情弹窗 -->
    <Dialog
      width="35%"
      v-model="showDialogForm"
      :title="dialogMode === 'add' ? '新增数据集' : dialogMode === 'edit' ? '编辑数据集' : '数据集详情'"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      :visible-confirm-button="dialogMode !== 'view'"
      :confirm-text="dialogMode === 'add' ? '新增' : '保存'"
      @closed="currentRow = null; dialogForm = {}"
      @click-confirm="onDialogConfirm"
    >
      <!-- 基础信息表单 -->
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
        :disabled="dialogMode === 'view'"
      />
    </Dialog>



    <!-- 数据集类型配置弹窗 -->
    <DataSetTypeConfigDialog
      v-model="showDataSetTypeConfigDialog"
    />

    <!-- 数据集分类弹窗 -->
    <DataSetCategoryDialog
      v-model="showDataSetCategoryDialog"
    />

    <!-- 质量监控与评估弹窗 -->
    <QualityMonitoringAssessmentDialog
      v-model="showQualityMonitoringAssessmentDialog"
    />

    <!-- 操作按钮隐藏配置弹窗 -->
    <OperationButtonConfigDialog
      v-model="showOperationButtonConfigDialog"
    />

    <!-- 访问权限调整弹窗 -->
    <Dialog
      v-model="showAccessPermissionDialog"
      title="访问权限调整"
      :destroy-on-close="true"
      width="600px"
      :visible-confirm-button="false"
    >
      <div class="dialog-content">
        <el-form :model="accessPermissionForm" label-width="100px">
          <el-form-item label="对象类型：">
            <el-select v-model="accessPermissionForm.objectType" style="width: 100%">
              <el-option label="用户" value="用户" />
              <el-option label="角色" value="角色" />
            </el-select>
          </el-form-item>

          <el-form-item label="用户/角色：">
            <el-select v-model="accessPermissionForm.objectSelection" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in getObjectSelectionData"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="权限范围：">
            <div style="display: flex; flex-wrap: wrap; gap: 20px;">
              <el-checkbox v-model="accessPermissionForm.readPermission">库级权限</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.tablePermission">表级权限</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.fieldPermission">字段权限</el-checkbox>
            </div>
          </el-form-item>

          <el-form-item label="权限设置：">
            <div style="display: flex; flex-wrap: wrap; gap: 20px;">
              <el-checkbox v-model="accessPermissionForm.viewPermission">查看</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.editPermission">编辑</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.addPermission">新增</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.deletePermission">删除</el-checkbox>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAccessPermissionDialog = false">取消</el-button>
          <el-button type="primary" @click="saveAccessPermission">确定</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 批量导入弹窗 -->
    <Dialog
      v-model="batchImportDialogVisible"
      title="数据集导入"
      width="600px"
      :destroy-on-close="true"
      :loading="importLoading"
      loading-text="导入中"
      confirm-text="上传"
      @click-confirm="uploadFile"
    >
      <div class="import-content">
        <div class="import-header">
          <el-icon><Upload /></el-icon>
          <span>导入须知</span>
        </div>

        <div class="import-steps">
          <div class="step-title">操作流程：</div>
          <div class="steps">
            <div class="step">
              <span class="step-number">1</span>
              <span class="step-text">下载模板</span>
            </div>
            <div class="step-divider"></div>
            <div class="step">
              <span class="step-number">2</span>
              <span class="step-text">填写表格</span>
            </div>
            <div class="step-divider"></div>
            <div class="step">
              <span class="step-number">3</span>
              <span class="step-text">上传表格</span>
            </div>
          </div>
        </div>

        <div class="template-download">
          <el-button type="primary" @click="downloadTemplate">
            <el-icon><Download /></el-icon>
            导入模板下载
          </el-button>
        </div>

        <div class="upload-area">
          <el-upload
            class="upload-demo"
            drag
            :file-list="importFileList"
            :on-remove="handleFileRemove"
            :on-change="handleFileSelect"
            :limit="1"
            accept=".xlsx,.xls"
            :auto-upload="false"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              点击或将文件拖拽到这里上传
            </div>
            <template #tip>
              <div class="el-upload__tip">
                导入模式为业务表中已有数据更新，业务表中不含数据新增，仅支持后缀名为xlsx文件
              </div>
            </template>
          </el-upload>

          <!-- 显示选中的文件名 -->
          <div v-if="selectedFileName" class="selected-file">
            <div class="file-name">{{ selectedFileName }}</div>
          </div>
        </div>
      </div>
    </Dialog>
    <!-- 数据备份与恢复规则弹窗 -->
    <Dialog
      v-model="showBackupRuleDialog"
      title="数据集备份与恢复"
      :destroy-on-close="true"
      width="800px"
      confirm-text="保存"
      cancel-text="取消"
      @click-confirm="saveBackupRuleToCache"
      @click-cancel="showBackupRuleDialog = false"
    >
      <div class="dialog-content">
        <!-- 数据集备份规则 -->
        <div class="section-title">数据集备份规则</div>
        <div class="form-section">
          <el-form :model="backupRuleForm" label-width="180px">
            <el-form-item label="数据备份开始时间：">
              <el-date-picker
                v-model="backupRuleForm.backupStartTime"
                type="datetime"
                placeholder="请选择日期时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>

            <el-form-item label="全量备份频率：">
              <el-select v-model="backupRuleForm.fullBackupFrequency" style="width: 100%">
                <el-option label="每小时" value="每小时" />
                <el-option label="每日" value="每日" />
                <el-option label="每周" value="每周" />
                <el-option label="每月" value="每月" />
              </el-select>
            </el-form-item>

            <el-form-item label="增量备份频率：">
              <el-select v-model="backupRuleForm.incrementalBackupFrequency" style="width: 100%">
                <el-option label="每小时" value="每小时" />
                <el-option label="每日" value="每日" />
                <el-option label="每周" value="每周" />
                <el-option label="每月" value="每月" />
              </el-select>
            </el-form-item>

            <el-form-item label="备份数据清理策略：">
              <el-select v-model="backupRuleForm.dataCleanupPolicy" style="width: 100%">
                <el-option label="保留30天" value="保留30天" />
                <el-option label="保留60天" value="保留60天" />
                <el-option label="保留120天" value="保留120天" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <!-- 数据集恢复启动规则 -->
        <div class="section-title">数据集恢复启动规则</div>
        <div class="form-section">
          <el-form :model="backupRuleForm" label-width="180px">
            <el-form-item label="请选择恢复至指定时期：">
              <el-date-picker
                v-model="backupRuleForm.recoveryToTime"
                type="datetime"
                placeholder="请选择日期时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>

            <el-form-item label="请选择恢复执行时间：">
              <el-date-picker
                v-model="backupRuleForm.recoveryExecutionTime"
                type="datetime"
                placeholder="请选择日期时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </Dialog>

    <!-- 数据集合规性检查弹窗 -->
    <Dialog
      v-model="showComplianceCheckDialog"
      title="数据集合规性检查"
      width="1100px"
      :destroy-on-close="true"
      confirm-text="保存配置"
      cancel-text="关闭"
      @click-confirm="saveComplianceConfig"
      @click-cancel="showComplianceCheckDialog = false"
    >
      <div class="compliance-check" style="padding: 20px; min-height: 400px;">
        <!-- 合规性检查规则管理 -->
        <div class="section">
          <div class="section-title">合规性检查规则管理</div>

          <!-- 操作按钮 -->
          <div class="operation-buttons" style="margin-bottom: 20px;">
            <el-button type="primary" @click="showAddRuleDialog = true">添加规则</el-button>
          </div>

          <!-- 规则列表表格 -->
          <el-table :data="ruleList" style="width: 100%" border>
            <el-table-column label="序号" width="80" align="center">
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="ruleName" label="规则名称" width="150" />
            <el-table-column prop="applicableField" label="适用字段" width="120" />
            <el-table-column prop="ruleType" label="规则类型" width="120" />
            <el-table-column prop="violationHandling" label="违规处理方式" width="120" />
            <el-table-column prop="ruleStatus" label="规则状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.ruleStatus === '启用' ? 'success' : 'danger'">
                  {{ row.ruleStatus }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180" />
            <el-table-column label="操作" width="150" align="center">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleEditRule(row)">编辑</el-button>
                <el-popconfirm title="确认删除这条规则吗？" @confirm="handleDeleteRule(row)">
                  <template #reference>
                    <el-button type="danger" size="small">删除</el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 合规性检查配置 -->
        <div class="section" style="margin-top: 30px;">
          <div class="section-title">合规性检查配置</div>
          <el-form :model="checkConfigForm" label-width="120px" style="max-width: 600px;">
            <el-form-item label="检查频率：">
              <el-select v-model="checkConfigForm.frequency" style="width: 200px;">
                <el-option label="每日" value="每日" />
                <el-option label="每周" value="每周" />
                <el-option label="每月" value="每月" />
              </el-select>
            </el-form-item>
            <el-form-item label="执行时间：">
              <el-time-picker
                v-model="checkConfigForm.executionTime"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                placeholder="请选择执行时间"
                style="width: 200px;"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </Dialog>

    <!-- 添加规则弹窗 -->
    <Dialog
      v-model="showAddRuleDialog"
      :title="currentRule ? '编辑规则' : '添加规则'"
      width="600px"
      :destroy-on-close="true"
      :loading="ruleFormLoading"
      loading-text="保存中"
      confirm-text="保存"
      cancel-text="取消"
      @click-confirm="handleSaveRule"
      @click-cancel="showAddRuleDialog = false; resetRuleForm()"
    >
      <el-form :model="ruleForm" label-width="120px">
        <el-form-item label="规则名称：" required>
          <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="适用字段：" required>
          <el-select v-model="ruleForm.applicableField" placeholder="请选择适用字段" style="width: 100%">
            <el-option label="数据集名称" value="数据集名称" />
            <el-option label="数据集类型" value="数据集类型" />
            <el-option label="数据集分类" value="数据集分类" />
            <el-option label="数据源名称" value="数据源名称" />
            <el-option label="更新时间" value="更新时间" />
            <el-option label="创建人" value="创建人" />
            <el-option label="描述" value="描述" />
          </el-select>
        </el-form-item>
        <el-form-item label="规则类型：" required>
          <el-select v-model="ruleForm.ruleType" placeholder="请选择规则类型" style="width: 100%">
            <el-option label="格式校验" value="格式校验" />
            <el-option label="范围校验" value="范围校验" />
            <el-option label="必填校验" value="必填校验" />
            <el-option label="自定义校验" value="自定义校验" />
          </el-select>
        </el-form-item>
        <el-form-item label="违规处理方式：" required>
          <el-select v-model="ruleForm.violationHandling" placeholder="请选择违规处理方式" style="width: 100%">
            <el-option label="仅告警" value="仅告警" />
            <el-option label="阻止记录" value="阻止记录" />
            <el-option label="自动修复" value="自动修复" />
          </el-select>
        </el-form-item>
        <el-form-item label="规则状态：" required>
          <el-select v-model="ruleForm.ruleStatus" placeholder="请选择规则状态" style="width: 100%">
            <el-option label="启用" value="启用" />
            <el-option label="禁用" value="禁用" />
          </el-select>
        </el-form-item>
        <el-form-item label="规则描述：">
          <el-input
            v-model="ruleForm.ruleDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入规则描述"
          />
        </el-form-item>
      </el-form>
    </Dialog>
  </div>
</template>

<script setup lang="ts" name="DataSetTab">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, Upload, Download, UploadFilled } from '@element-plus/icons-vue'
import * as ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'

// 导入弹窗组件
import DataSetTypeConfigDialog from './DataSetTypeConfigDialog.vue'
import DataSetCategoryDialog from './DataSetCategoryDialog.vue'
import QualityMonitoringAssessmentDialog from './QualityMonitoringAssessmentDialog.vue'
import OperationButtonConfigDialog from './OperationButtonConfigDialog.vue'

// 数据集类型定义
interface DataSet {
  id: string
  name: string
  description: string
  type: string
  category: string
  status: boolean
  createTime: string
  createUser: string
  dataSize: string
  recordCount: number
  lastUpdateTime: string
  dataSourceName: string
  dataSourceType: string
}

// 响应式数据
const loading = ref(false)
const tableHeight = ref(400)
const currentRow = ref<DataSet | null>(null)
const selectedRows = ref<DataSet[]>([])

// 搜索表单数据

const searchForm = ref({
  name: '',
  dataSourceName: '',
  dataSourceType: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表格列配置
const tableColumns = ref([
  { field: 'name', title: '数据集名称', minWidth: 150 },
  { field: 'description', title: '描述', minWidth: 200 },
  { field: 'dataSourceName', title: '数据源名称', minWidth: 150 },
  { field: 'dataSourceType', title: '数据源类型', width: 120 },
  { field: 'type', title: '数据集类型', width: 120 },
  { field: 'category', title: '分类', width: 100 },
  { field: 'dataSize', title: '数据大小', width: 100 },
  { field: 'recordCount', title: '记录数', width: 100 },
  { field: 'status', title: '状态', width: 80 },
  { field: 'createTime', title: '创建时间', width: 150 },
  { field: 'createUser', title: '创建人', width: 100 },
  { field: 'lastUpdateTime', title: '最后更新时间', width: 150 }
])

// 表格操作按钮
const tableButtons = ref([
  { title: '详情', type: 'info', code: 'view', verify: 'true' },
  { title: '修改', type: 'primary', code: 'edit', verify: 'true' },
  { title: '删除', type: 'danger', code: 'delete', verify: 'true' }
])

// 弹窗相关
const showDialogForm = ref(false)
const dialogMode = ref<'add' | 'edit' | 'view'>('add')
const dialogForm = ref<Partial<DataSet>>({})

// 弹窗表单配置
const dialogFormProps = computed(() => [
  { label: '数据集名称', prop: 'name', type: 'text', required: true },
  { label: '描述', prop: 'description', type: 'textarea', required: true },
  { label: '数据集类型', prop: 'type', type: 'select', required: true, options: [
    { label: '结构化数据', value: 'structured' },
    { label: '半结构化数据', value: 'semi-structured' },
    { label: '非结构化数据', value: 'unstructured' }
  ]},
  { label: '数据集分类', prop: 'category', type: 'select', required: true, options: [
    { label: '业务数据', value: 'business' },
    { label: '日志数据', value: 'log' },
    { label: '监控数据', value: 'monitor' }
  ]},
  { label: '状态', prop: 'status', type: 'switch', required: true }
])

// 表单验证规则
const dialogFormRules = {
  name: [{ required: true, message: '请输入数据集名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  type: [{ required: true, message: '请选择数据集类型', trigger: 'change' }],
  category: [{ required: true, message: '请选择数据集分类', trigger: 'change' }]
}

// 各种弹窗显示状态
const showDataSetTypeConfigDialog = ref(false)
const showDataSetCategoryDialog = ref(false)
const showQualityMonitoringAssessmentDialog = ref(false)
const showOperationButtonConfigDialog = ref(false)

// 访问权限管理相关数据
const showAccessPermissionDialog = ref(false)
const accessPermissionForm = ref({
  objectType: '用户',
  objectSelection: '',
  readPermission: false,
  tablePermission: false,
  fieldPermission: false,
  viewPermission: false,
  editPermission: false,
  addPermission: false,
  deletePermission: false
})

// 用户和角色数据
const userData = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
])

const roleData = ref([
  { id: 1, name: '系统管理员' },
  { id: 2, name: '普通用户' }
])

// 权限审计数据存储
const permissionAuditData = ref<any[]>([])

// 数据备份与恢复规则弹窗
const showBackupRuleDialog = ref(false)
const backupRuleForm = ref({
  backupStartTime: '',
  fullBackupFrequency: '每小时',
  incrementalBackupFrequency: '每小时',
  dataCleanupPolicy: '保留30天',
  recoveryToTime: '',
  recoveryExecutionTime: ''
})

// 数据集合规性检查弹窗
const showComplianceCheckDialog = ref(false)
const ruleList = ref([
  {
    id: 1,
    ruleName: '数据集名称格式校验',
    applicableField: '数据集名称',
    ruleType: '格式校验',
    violationHandling: '仅告警',
    ruleStatus: '启用',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    ruleName: '更新时间必填',
    applicableField: '更新时间',
    ruleType: '必填校验',
    violationHandling: '阻止记录',
    ruleStatus: '启用',
    createTime: '2024-01-16 14:20:00'
  }
])

// 检查配置表单
const checkConfigForm = ref({
  frequency: '每日',
  executionTime: '02:00:00'
})

// 添加规则弹窗状态
const showAddRuleDialog = ref(false)
const ruleFormLoading = ref(false)
const currentRule = ref<any>(null)

// 规则表单
const ruleForm = ref({
  ruleName: '',
  applicableField: '',
  ruleType: '',
  violationHandling: '',
  ruleStatus: '启用',
  ruleDescription: ''
})

// 批量导入相关数据
const batchImportDialogVisible = ref(false)
const importFileList = ref<any[]>([])
const importLoading = ref(false)
const selectedFileName = ref('')

// 缓存键
const STORAGE_KEY = 'dataSetManagement_data'

// 模拟数据 - 直接在页面中初始化
const dataSetList = ref<DataSet[]>([
  {
    id: '1',
    name: '客户关系管理数据集',
    description: '包含客户基本信息、联系记录、购买历史等完整CRM数据',
    type: 'structured',
    category: 'business',
    status: true,
    createTime: '2024-01-15 10:30:00',
    createUser: '张建华',
    dataSize: '3.2GB',
    recordCount: 1850000,
    lastUpdateTime: '2024-07-20 14:20:00',
    dataSourceName: 'MySQL客户数据库',
    dataSourceType: 'MySql'
  },
  {
    id: '2',
    name: '应用系统日志数据集',
    description: '生产环境应用服务器日志，包含访问日志、错误日志、性能日志',
    type: 'semi-structured',
    category: 'log',
    status: true,
    createTime: '2024-02-14 09:15:00',
    createUser: '李明',
    dataSize: '8.7GB',
    recordCount: 12500000,
    lastUpdateTime: '2024-07-21 16:45:00',
    dataSourceName: 'Oracle日志数据库',
    dataSourceType: 'Oracle'
  },
  {
    id: '3',
    name: '服务器性能监控数据集',
    description: '服务器CPU、内存、磁盘、网络等性能指标实时监控数据',
    type: 'structured',
    category: 'monitor',
    status: true,
    createTime: '2024-03-13 15:20:00',
    createUser: '王强',
    dataSize: '2.1GB',
    recordCount: 5600000,
    lastUpdateTime: '2024-07-19 11:30:00',
    dataSourceName: 'SQL Server监控库',
    dataSourceType: 'SQL Server'
  },
  {
    id: '4',
    name: '电商交易流水数据集',
    description: '在线商城订单、支付、退款、物流等完整交易链路数据',
    type: 'structured',
    category: 'business',
    status: true,
    createTime: '2024-04-12 16:45:00',
    createUser: '赵丽',
    dataSize: '15.3GB',
    recordCount: 8900000,
    lastUpdateTime: '2024-07-22 09:30:00',
    dataSourceName: '达梦交易数据库',
    dataSourceType: '达梦'
  },
  {
    id: '5',
    name: '智能制造设备数据集',
    description: '工业4.0智能制造设备传感器数据、生产参数、质量检测数据',
    type: 'unstructured',
    category: 'iot',
    status: true,
    createTime: '2024-05-11 11:20:00',
    createUser: '孙伟',
    dataSize: '22.8GB',
    recordCount: 45600000,
    lastUpdateTime: '2024-07-23 15:10:00',
    dataSourceName: 'MongoDB设备数据库',
    dataSourceType: 'MangoDB'
  },
  {
    id: '6',
    name: '大数据用户画像分析集',
    description: '基于多维度用户行为的画像标签、偏好分析、推荐算法训练数据',
    type: 'structured',
    category: 'analytics',
    status: true,
    createTime: '2024-06-10 14:30:00',
    createUser: '周敏',
    dataSize: '28.5GB',
    recordCount: 15600000,
    lastUpdateTime: '2024-07-24 10:15:00',
    dataSourceName: 'Hive数据仓库',
    dataSourceType: 'Hive'
  },
  {
    id: '7',
    name: '金融风控模型数据集',
    description: '银行信贷风险评估、反欺诈检测、合规监管等金融风控数据',
    type: 'structured',
    category: 'finance',
    status: true,
    createTime: '2024-01-09 09:45:00',
    createUser: '吴刚',
    dataSize: '9.2GB',
    recordCount: 3200000,
    lastUpdateTime: '2024-07-25 16:20:00',
    dataSourceName: '华为云GaussDB',
    dataSourceType: 'Huawei GaussDB'
  },
  {
    id: '8',
    name: '医疗健康档案数据集',
    description: '患者电子病历、检查报告、药物处方、健康体检等医疗数据',
    type: 'semi-structured',
    category: 'healthcare',
    status: false,
    createTime: '2024-02-08 13:25:00',
    createUser: '陈静',
    dataSize: '18.6GB',
    recordCount: 2800000,
    lastUpdateTime: '2024-07-18 12:40:00',
    dataSourceName: 'Oracle医疗数据库',
    dataSourceType: 'Oracle'
  },
  {
    id: '9',
    name: '教育在线学习数据集',
    description: '在线教育平台学习行为、课程评价、考试成绩、学习路径等数据',
    type: 'structured',
    category: 'education',
    status: true,
    createTime: '2024-03-07 16:10:00',
    createUser: '刘洋',
    dataSize: '7.4GB',
    recordCount: 6700000,
    lastUpdateTime: '2024-07-17 14:55:00',
    dataSourceName: 'MySQL教育数据库',
    dataSourceType: 'MySql'
  },
  {
    id: '10',
    name: '智慧城市交通数据集',
    description: '城市交通流量、路况信息、公共交通、停车场使用等智慧交通数据',
    type: 'structured',
    category: 'smart_city',
    status: true,
    createTime: '2024-04-06 08:30:00',
    createUser: '黄磊',
    dataSize: '31.2GB',
    recordCount: 89500000,
    lastUpdateTime: '2024-07-16 18:25:00',
    dataSourceName: 'SQL Server交通数据库',
    dataSourceType: 'SQL Server'
  }
])

// 初始化模拟数据（已移至dataSetList初始化中，保留函数用于重置数据）
const initMockData = () => {
  // 数据已在dataSetList初始化时设置，这里只需要保存到缓存
  saveDataToCache()
}

// 缓存操作 - 不再使用，保留函数以防后续需要
const loadDataFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      // 不再从缓存加载数据，使用页面中初始化的数据
      console.log('缓存中有数据，但使用页面初始化的数据')
    }
  } catch (error) {
    console.error('加载数据集数据失败:', error)
  }
}

const saveDataToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataSetList.value))
  } catch (error) {
    console.error('保存数据集数据失败:', error)
  }
}

// 计算属性
const filteredData = computed(() => {
  let filtered = dataSetList.value

  if (searchForm.value.name) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchForm.value.name.toLowerCase())
    )
  }

  if (searchForm.value.dataSourceName) {
    filtered = filtered.filter(item =>
      item.dataSourceName.toLowerCase().includes(searchForm.value.dataSourceName.toLowerCase())
    )
  }

  if (searchForm.value.dataSourceType) {
    filtered = filtered.filter(item => item.dataSourceType === searchForm.value.dataSourceType)
  }

  pagination.total = filtered.length
  return filtered
})

const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filteredData.value.slice(start, end)
})

// 事件处理
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 200
}

const onSearch = () => {
  pagination.page = 1
}

const onReset = () => {
  searchForm.value = {
    name: '',
    dataSourceName: '',
    dataSourceType: ''
  }
  pagination.page = 1
}

const onPageChange = (page: number) => {
  pagination.page = page
}

const onSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
}

const onSelectionChange = (selection: DataSet[]) => {
  selectedRows.value = selection
}

// 顶部按钮事件
const onClickAdd = () => {
  dialogMode.value = 'add'
  currentRow.value = null
  dialogForm.value = {}
  showDialogForm.value = true
}

const onClickBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 条数据吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const selectedIds = selectedRows.value.map(row => row.id)
    dataSetList.value = dataSetList.value.filter(item => !selectedIds.includes(item.id))
    saveDataToCache()
    ElMessage.success('删除成功')
  })
}

// 批量导出
const onClickBatchExport = async () => {
  // 确定要导出的数据：如果有选中数据则导出选中的，否则导出全部数据
  const exportData = selectedRows.value.length > 0 ? selectedRows.value : filteredData.value
  const exportType = selectedRows.value.length > 0 ? '选中' : '全部'

  if (exportData.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  try {
    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('数据集数据')

    // 设置表头
    const headers = [
      { header: '序号', key: 'id', width: 10 },
      { header: '数据集名称', key: 'name', width: 30 },
      { header: '数据集类型', key: 'type', width: 15 },
      { header: '数据集分类', key: 'category', width: 15 },
      { header: '数据源', key: 'dataSource', width: 20 },
      { header: '状态', key: 'status', width: 10 },
      { header: '描述', key: 'description', width: 30 },
      { header: '创建时间', key: 'createTime', width: 15 },
      { header: '更新时间', key: 'updateTime', width: 15 }
    ]

    worksheet.columns = headers

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 添加数据
    exportData.forEach((row, index) => {
      worksheet.addRow({
        id: index + 1,
        name: row.name,
        type: row.type,
        category: row.category,
        dataSource: row.dataSource,
        status: row.status ? '启用' : '停用',
        description: row.description || '',
        createTime: row.createTime,
        updateTime: row.updateTime
      })
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

    // 下载文件
    const fileName = `数据集数据_${exportType}_${new Date().toISOString().slice(0, 10)}.xlsx`
    saveAs(blob, fileName)

    ElMessage.success(`成功导出${exportType} ${exportData.length} 条数据`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 批量导入
const onClickBatchImport = () => {
  batchImportDialogVisible.value = true
  importFileList.value = []
  selectedFileName.value = ''
}

const onClickUpdateLog = () => {
  ElMessage.info('更新日志功能开发中...')
}

const onClickFieldTemplate = () => {
  ElMessage.info('字段模板功能开发中...')
}

// 新增的弹窗打开事件
const onClickOperationButtonConfig = () => {
  showOperationButtonConfigDialog.value = true
}

const onClickDataSetTypeConfig = () => {
  showDataSetTypeConfigDialog.value = true
}

const onClickDataSetCategory = () => {
  showDataSetCategoryDialog.value = true
}

const onClickQualityMonitoringAssessment = () => {
  showQualityMonitoringAssessmentDialog.value = true
}

// 下载导入模板
const downloadTemplate = async () => {
  try {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('数据集模板')

    // 设置表头
    const headers = [
      { header: '数据集名称', key: 'name', width: 30 },
      { header: '数据集类型', key: 'type', width: 15 },
      { header: '数据集分类', key: 'category', width: 15 },
      { header: '数据源', key: 'dataSource', width: 20 },
      { header: '描述', key: 'description', width: 30 }
    ]

    worksheet.columns = headers

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 添加示例数据
    worksheet.addRow({
      name: '示例数据集名称',
      type: '结构化数据',
      category: '业务数据',
      dataSource: '示例数据源',
      description: '示例描述信息'
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

    // 下载文件
    const fileName = `数据集导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`
    saveAs(blob, fileName)

    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('模板下载失败:', error)
    ElMessage.error('模板下载失败，请重试')
  }
}

// 处理文件选择（on-change事件）
const handleFileSelect = (file: any, fileList: any[]) => {
  console.log('文件选择事件:', file, fileList)

  // 检查文件类型
  if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
    ElMessage.error('请上传Excel文件(.xlsx或.xls格式)')
    // 清理无效文件
    importFileList.value = []
    selectedFileName.value = ''
    return
  }

  // 检查文件大小（限制为10MB）
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    // 清理无效文件
    importFileList.value = []
    selectedFileName.value = ''
    return
  }

  // 保存文件信息
  importFileList.value = [file]
  selectedFileName.value = file.name
  ElMessage.success('文件选择成功，点击上传按钮开始导入')
}



// 处理文件移除
const handleFileRemove = () => {
  importFileList.value = []
  selectedFileName.value = ''
}

// 上传并解析Excel文件
const uploadFile = async () => {
  console.log('开始上传文件:', importFileList.value, selectedFileName.value)

  if (importFileList.value.length === 0 || !selectedFileName.value) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  // 获取文件对象，优先使用raw属性
  const file = importFileList.value[0].raw || importFileList.value[0]
  console.log('获取到的文件对象:', file)

  if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
    ElMessage.error('请上传Excel文件(.xlsx或.xls格式)')
    return
  }

  importLoading.value = true

  try {
    const buffer = await file.arrayBuffer()
    const workbook = new ExcelJS.Workbook()
    await workbook.xlsx.load(buffer)

    const worksheet = workbook.getWorksheet(1)
    if (!worksheet) {
      throw new Error('Excel文件中没有找到工作表')
    }

    const importData: DataSet[] = []
    const errors: string[] = []
    const validTypes = ['结构化数据', '半结构化数据', '非结构化数据', '流式数据']
    const validCategories = ['业务数据', '日志数据', '监控数据', '分析数据']

    // 从第二行开始读取数据（第一行是表头）
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber === 1) return // 跳过表头

      const values = row.values as any[]
      const name = values[1]?.toString()?.trim()
      const type = values[2]?.toString()?.trim()
      const category = values[3]?.toString()?.trim()
      const dataSource = values[4]?.toString()?.trim()
      const description = values[5]?.toString()?.trim() || ''

      // 跳过完全空白的行
      if (!name && !type && !category && !dataSource) {
        return
      }

      // 验证必填字段
      if (!name) {
        errors.push(`第${rowNumber}行：数据集名称不能为空`)
        return
      }
      if (!type) {
        errors.push(`第${rowNumber}行：数据集类型不能为空`)
        return
      }
      if (!category) {
        errors.push(`第${rowNumber}行：数据集分类不能为空`)
        return
      }
      if (!dataSource) {
        errors.push(`第${rowNumber}行：数据源不能为空`)
        return
      }

      // 验证字段类型是否有效
      if (!validTypes.includes(type)) {
        errors.push(`第${rowNumber}行：数据集类型"${type}"无效，请使用：${validTypes.join('、')}`)
        return
      }

      // 验证分类是否有效
      if (!validCategories.includes(category)) {
        errors.push(`第${rowNumber}行：数据集分类"${category}"无效，请使用：${validCategories.join('、')}`)
        return
      }

      // 检查数据集名称是否重复
      const existingItem = dataSetList.value.find(item => item.name === name)
      if (existingItem) {
        errors.push(`第${rowNumber}行：数据集名称"${name}"已存在`)
        return
      }

      // 检查导入数据中是否有重复
      const duplicateInImport = importData.find(item => item.name === name)
      if (duplicateInImport) {
        errors.push(`第${rowNumber}行：数据集名称"${name}"在导入数据中重复`)
        return
      }

      const newItem: DataSet = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name,
        type,
        category,
        dataSource,
        status: true,
        description,
        createTime: new Date().toLocaleString(),
        createUser: '当前用户',
        dataSize: '0MB',
        recordCount: 0,
        lastUpdateTime: new Date().toLocaleString(),
        updateUser: '当前用户',
        dataSourceType: 'MySql'
      }

      importData.push(newItem)
    })

    if (errors.length > 0) {
      // 限制错误信息显示数量，避免过长
      const displayErrors = errors.slice(0, 10)
      const errorMessage = displayErrors.join('\n') + (errors.length > 10 ? `\n...还有${errors.length - 10}个错误` : '')
      ElMessage.error(`导入失败，发现以下错误：\n${errorMessage}`)
      return
    }

    if (importData.length === 0) {
      ElMessage.warning('没有找到有效的数据，请检查Excel文件内容')
      return
    }

    // 添加到表格数据
    dataSetList.value.push(...importData)
    saveDataToCache()

    ElMessage.success(`成功导入 ${importData.length} 条数据`)
    batchImportDialogVisible.value = false
    importFileList.value = []
    selectedFileName.value = ''

  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error(`文件解析失败：${error.message || '请检查文件格式'}`)
  } finally {
    importLoading.value = false
  }
}

const onClickDataSetSync = () => {
  ElMessage.info('数据集同步功能开发中...')
}

// 数据备份与恢复下拉菜单
const handleDataBackupCommand = (command: string) => {
  switch (command) {
    case 'backupRule':
      onClickBackupRule()
      break
    case 'backupVerify':
      ElMessage.info('数据备份与恢复验证功能开发中...')
      break
  }
}

// 数据备份与恢复规则按钮
const onClickBackupRule = () => {
  loadBackupRuleFromCache()
  showBackupRuleDialog.value = true
}

// 加载备份规则配置
const loadBackupRuleFromCache = () => {
  try {
    const cached = localStorage.getItem('dataSetBackupRule')
    if (cached) {
      const config = JSON.parse(cached)
      backupRuleForm.value = {
        backupStartTime: config.backupStartTime || '',
        fullBackupFrequency: config.fullBackupFrequency || '每小时',
        incrementalBackupFrequency: config.incrementalBackupFrequency || '每小时',
        dataCleanupPolicy: config.dataCleanupPolicy || '保留30天',
        recoveryToTime: config.recoveryToTime || '',
        recoveryExecutionTime: config.recoveryExecutionTime || ''
      }
    }
  } catch (error) {
    console.error('加载数据集备份规则失败:', error)
  }
}

// 保存备份规则配置
const saveBackupRuleToCache = () => {
  try {
    localStorage.setItem('dataSetBackupRule', JSON.stringify(backupRuleForm.value))
    ElMessage.success('数据集备份与恢复规则保存成功')
    showBackupRuleDialog.value = false
  } catch (error) {
    console.error('保存数据集备份规则失败:', error)
    ElMessage.error('保存失败')
  }
}

// 合规性检查相关函数
// 打开合规性检查弹窗
const onClickComplianceCheck = () => {
  loadComplianceConfig()
  showComplianceCheckDialog.value = true
}

// 加载合规性检查配置
const loadComplianceConfig = () => {
  try {
    const cached = localStorage.getItem('dataSetComplianceCheck_data')
    if (cached) {
      const config = JSON.parse(cached)
      if (config.checkConfig) {
        checkConfigForm.value = { ...checkConfigForm.value, ...config.checkConfig }
      }
      if (config.rules) {
        ruleList.value = config.rules
      }
    }
  } catch (error) {
    console.error('加载数据集合规性检查配置失败:', error)
  }
}

// 保存合规性检查配置
const saveComplianceConfig = () => {
  try {
    const config = {
      checkConfig: checkConfigForm.value,
      rules: ruleList.value,
      updateTime: new Date().toISOString()
    }
    localStorage.setItem('dataSetComplianceCheck_data', JSON.stringify(config))

    ElMessage.success('数据集合规性检查配置保存成功')
    showComplianceCheckDialog.value = false
  } catch (error) {
    console.error('保存数据集合规性检查配置失败:', error)
    ElMessage.error('保存失败')
  }
}

// 保存规则到缓存
const saveRulesToCache = () => {
  try {
    const config = {
      checkConfig: checkConfigForm.value,
      rules: ruleList.value,
      updateTime: new Date().toISOString()
    }
    localStorage.setItem('dataSetComplianceCheck_data', JSON.stringify(config))
  } catch (error) {
    console.error('保存规则到缓存失败:', error)
  }
}

// 编辑规则
const handleEditRule = (row: any) => {
  currentRule.value = row
  ruleForm.value = { ...row }
  showAddRuleDialog.value = true
}

// 删除规则
const handleDeleteRule = (row: any) => {
  const index = ruleList.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    ruleList.value.splice(index, 1)
    saveRulesToCache()
    ElMessage.success('删除规则成功')
  }
}

// 保存规则
const handleSaveRule = () => {
  ruleFormLoading.value = true

  setTimeout(() => {
    try {
      if (currentRule.value) {
        // 编辑模式
        const index = ruleList.value.findIndex(item => item.id === currentRule.value.id)
        if (index !== -1) {
          ruleList.value[index] = {
            ...ruleForm.value,
            id: currentRule.value.id,
            createTime: currentRule.value.createTime
          }
        }
      } else {
        // 新增模式
        const newRule = {
          ...ruleForm.value,
          id: Date.now(),
          createTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
        }
        ruleList.value.push(newRule)
      }

      saveRulesToCache()
      showAddRuleDialog.value = false
      resetRuleForm()
      ElMessage.success(currentRule.value ? '编辑规则成功' : '添加规则成功')
    } catch (error) {
      console.error('保存规则失败:', error)
      ElMessage.error('保存失败')
    } finally {
      ruleFormLoading.value = false
    }
  }, 1000)
}

// 重置规则表单
const resetRuleForm = () => {
  currentRule.value = null
  ruleForm.value = {
    ruleName: '',
    applicableField: '',
    ruleType: '',
    violationHandling: '',
    ruleStatus: '启用',
    ruleDescription: ''
  }
}

// 数据集审计下拉菜单
const handleDataAuditCommand = (command: string) => {
  switch (command) {
    case 'permissionAudit':
      ElMessage.info('数据集权限审计功能开发中...')
      break
    case 'accessAudit':
      ElMessage.info('数据集访问审计功能开发中...')
      break
  }
}

// 更多操作下拉菜单
const handleMoreCommand = (command: string) => {
  switch (command) {
    case 'exceptionHandling':
      onClickAccessPermission()
      break
    case 'qualityReportManagement':
      onClickBackupRule()
      break
    case 'dataSetIdentification':
      onClickComplianceCheck()
      break
    case 'integrationStrategy':
      ElMessage.info('集成策略与任务功能开发中...')
      break
    case 'transformationRule':
      ElMessage.info('转换规则与任务功能开发中...')
      break
    case 'formatFileConfig':
      ElMessage.info('格式文件配置功能开发中...')
      break
    case 'dataSetAccessRule':
      ElMessage.info('数据集接入规则功能开发中...')
      break
    case 'accessAnalysisMonitor':
      ElMessage.info('访问与分析监控功能开发中...')
      break
    case 'dataSetHealthMonitor':
      ElMessage.info('数据集健康监控功能开发中...')
      break
    case 'sourceAnalysis':
      ElMessage.info('来源分析功能开发中...')
      break
    case 'archiveManagement':
      ElMessage.info('归档功能开发中...')
      break
  }
}

// 访问权限管理相关函数
// 加载权限数据
const loadAccessPermissionData = () => {
  try {
    const savedData = localStorage.getItem('dataSetPermissionAuditData')
    if (savedData) {
      permissionAuditData.value = JSON.parse(savedData)
    }

    // 加载当前数据集的权限设置（暂时使用第一个数据集作为示例）
    const currentDataSetName = dataSetList.value[0]?.name
    if (currentDataSetName) {
      const existingPermission = permissionAuditData.value.find(
        item => item.dataSetName === currentDataSetName
      )

      if (existingPermission) {
        // 回显已保存的权限设置
        accessPermissionForm.value = {
          objectType: existingPermission.objectType,
          objectSelection: existingPermission.objectName,
          readPermission: existingPermission.readPermission,
          tablePermission: existingPermission.tablePermission,
          fieldPermission: existingPermission.fieldPermission,
          viewPermission: existingPermission.viewPermission,
          editPermission: existingPermission.editPermission,
          addPermission: existingPermission.addPermission,
          deletePermission: existingPermission.deletePermission
        }
        return
      }
    }

    // 如果没有找到已保存的数据，使用默认值
    accessPermissionForm.value = {
      objectType: '用户',
      objectSelection: '',
      readPermission: false,
      tablePermission: false,
      fieldPermission: false,
      viewPermission: false,
      editPermission: false,
      addPermission: false,
      deletePermission: false
    }
  } catch (error) {
    console.error('加载访问权限数据失败:', error)
    // 出错时使用默认值
    accessPermissionForm.value = {
      objectType: '用户',
      objectSelection: '',
      readPermission: false,
      tablePermission: false,
      fieldPermission: false,
      viewPermission: false,
      editPermission: false,
      addPermission: false,
      deletePermission: false
    }
  }
}

// 打开访问权限调整弹窗
const onClickAccessPermission = () => {
  loadAccessPermissionData()
  showAccessPermissionDialog.value = true
}

// 根据对象类型获取对象选择数据
const getObjectSelectionData = computed(() => {
  return accessPermissionForm.value.objectType === '用户' ? userData.value : roleData.value
})

// 保存访问权限设置
const saveAccessPermission = () => {
  // 使用第一个数据集作为示例（实际应用中可能需要其他方式确定当前数据集）
  const currentDataSetName = dataSetList.value[0]?.name
  if (!currentDataSetName) {
    ElMessage.error('没有可用的数据集')
    return
  }

  // 检查是否已存在该数据集的权限设置
  const existingIndex = permissionAuditData.value.findIndex(
    item => item.dataSetName === currentDataSetName
  )

  const permissionData = {
    id: existingIndex >= 0 ? permissionAuditData.value[existingIndex].id : Date.now(),
    dataSetName: currentDataSetName,
    objectType: accessPermissionForm.value.objectType,
    objectName: accessPermissionForm.value.objectSelection,
    readPermission: accessPermissionForm.value.readPermission,
    tablePermission: accessPermissionForm.value.tablePermission,
    fieldPermission: accessPermissionForm.value.fieldPermission,
    viewPermission: accessPermissionForm.value.viewPermission,
    editPermission: accessPermissionForm.value.editPermission,
    addPermission: accessPermissionForm.value.addPermission,
    deletePermission: accessPermissionForm.value.deletePermission,
    createTime: existingIndex >= 0 ? permissionAuditData.value[existingIndex].createTime : new Date().toLocaleString('zh-CN'),
    updateTime: new Date().toLocaleString('zh-CN')
  }

  if (existingIndex >= 0) {
    // 更新已存在的权限设置
    permissionAuditData.value[existingIndex] = permissionData
  } else {
    // 添加新的权限设置
    permissionAuditData.value.push(permissionData)
  }

  // 保存到localStorage
  localStorage.setItem('dataSetPermissionAuditData', JSON.stringify(permissionAuditData.value))

  ElMessage.success('访问权限调整保存成功')
  showAccessPermissionDialog.value = false
}

// 表格按钮点击事件
const onTableButtonClick = (data: any) => {
  console.log('onTableButtonClick data:', data)

  let btn, row

  // 处理不同的调用格式
  if (data && typeof data === 'object' && data.btn && data.scope) {
    // 格式1: { btn, scope } - 来自普通按钮点击
    btn = data.btn
    row = data.scope
  } else if (data && data.code) {
    // 格式2: 直接传递按钮对象 - 来自ElPopconfirm的@confirm事件
    // 这种情况下，第二个参数是行数据，但这里只有一个参数
    // 我们需要从当前作用域获取行数据
    btn = data
    row = arguments[1] // 第二个参数是行数据
  } else {
    console.error('无法解析按钮点击数据:', data, arguments)
    return
  }

  console.log('解析后的 btn:', btn, 'row:', row)

  if (!btn || !row) {
    console.error('按钮或行数据缺失:', { btn, row })
    return
  }

  currentRow.value = row

  switch (btn.code) {
    case 'view':
      dialogMode.value = 'view'
      dialogForm.value = { ...row }
      showDialogForm.value = true
      break
    case 'edit':
      dialogMode.value = 'edit'
      dialogForm.value = { ...row }
      showDialogForm.value = true
      break
    case 'delete':
      // 对于删除按钮，ElPopconfirm已经处理了确认逻辑
      // 直接执行删除操作
      const index = dataSetList.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        dataSetList.value.splice(index, 1)
        saveDataToCache()
        ElMessage.success('删除成功')
        console.log('删除成功，剩余数据条数:', dataSetList.value.length)
      }
      break
  }
}

// 弹窗确认
const onDialogConfirm = () => {
  if (dialogMode.value === 'view') {
    showDialogForm.value = false
    return
  }

  // 表单验证逻辑
  loading.value = true

  setTimeout(() => {
    if (dialogMode.value === 'add') {
      const newDataSet: DataSet = {
        ...dialogForm.value as DataSet,
        id: Date.now().toString(),
        createTime: new Date().toLocaleString(),
        createUser: '当前用户',
        dataSize: '0MB',
        recordCount: 0,
        lastUpdateTime: new Date().toLocaleString(),
        dataSourceName: '新建数据源',
        dataSourceType: 'MySql'
      }
      dataSetList.value.unshift(newDataSet)
      ElMessage.success('新增成功')
    } else if (dialogMode.value === 'edit') {
      const index = dataSetList.value.findIndex(item => item.id === currentRow.value?.id)
      if (index !== -1) {
        dataSetList.value[index] = {
          ...dataSetList.value[index],
          ...dialogForm.value,
          lastUpdateTime: new Date().toLocaleString()
        }
        ElMessage.success('编辑成功')
      }
    }

    saveDataToCache()
    showDialogForm.value = false
    loading.value = false
  }, 1000)
}

// 组件挂载 - 数据已在dataSetList中初始化，不需要额外加载
onMounted(() => {
  // 加载权限审计数据
  try {
    const savedData = localStorage.getItem('dataSetPermissionAuditData')
    if (savedData) {
      permissionAuditData.value = JSON.parse(savedData)
    }
  } catch (error) {
    console.error('加载权限审计数据失败:', error)
  }

  // 数据已在页面初始化时设置，无需从缓存加载
  console.log('数据集页面已挂载，数据条数:', dataSetList.value.length)
})
</script>

<style scoped>
.data-set-tab {
  height: 100%;
}

.top-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.search {
  margin-bottom: 20px;
}

.search-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-fields {
  flex: 1;
}

.search-buttons {
  display: flex;
  gap: 8px;
  margin-left: 20px;
}

/* 批量导入弹窗样式 */
.import-content {
  padding: 20px;
}

.import-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.import-steps {
  margin-bottom: 20px;
}

.step-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.steps {
  display: flex;
  align-items: center;
  gap: 10px;
}

.step {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
}

.step-text {
  font-size: 14px;
  color: #333;
}

.step-divider {
  width: 30px;
  height: 1px;
  background: #ddd;
}

.template-download {
  margin-bottom: 20px;
}

.upload-area {
  margin-top: 20px;
}

.selected-file {
  margin-top: 10px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.file-name {
  font-size: 14px;
  color: #333;
}

/* 备份规则弹窗样式 */
.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.form-section {
  margin-bottom: 24px;
}

.dialog-footer {
  margin-top: 20px;
  text-align: right;
}
</style>
